using Abstraction.Base.Response;
using MediatR;

namespace Application.Features.Funds.Queries.CheckUserAccess
{
    /// <summary>
    /// Query to check if the logged-in user has valid access to view a specific fund
    /// Based on user roles and fund associations (Fund Manager, Legal Council, Board Secretary, Board Member)
    /// </summary>
    public record CheckUserFundAccessQuery : IRequest<BaseResponse<UserFundAccessDto>>
    {
        public int FundId { get; set; }
    }
}
