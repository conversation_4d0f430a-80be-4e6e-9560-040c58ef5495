﻿using Abstraction.Contracts.Repository.Resolution;
using Domain.Entities.ResolutionManagement;

namespace Infrastructure.Data.Config
{

    public static class ResolutionStatusSeeds
    {
        public static async Task SeedResolutionStatusAsync(IResolutionStatusRepository resolutionStatusRepository)
        {
            var resolutionStatuses  =  new List<ResolutionStatus>
            {
                new ResolutionStatus
                {
                    Id = 1,
                    NameAr = "مسودة",
                    NameEn = "Draft"
                },
                new ResolutionStatus
                {
                    Id = 2,
                    NameAr = "معلق",
                    NameEn = "Pending"
                },
                new ResolutionStatus
                {
                    Id = 3,
                    NameAr = "استكمال البيانات",
                    NameEn = "Completing Data"
                },
                new ResolutionStatus
                {
                    Id = 4,
                    NameAr = "في انتظار التأكيد",
                    NameEn = "Waiting for Confirmation"
                },
                new ResolutionStatus
                {
                    Id = 5,
                    NameAr = "مؤكد",
                    NameEn = "Confirmed"
                },
                new ResolutionStatus
                {
                    Id = 6,
                    NameAr = "مرفوض",
                    NameEn = "Rejected"
                },
                new ResolutionStatus
                {
                    Id = 7,
                    NameAr = "التصويت قيد التقدم",
                    NameEn = "Voting in Progress"
                },
                new ResolutionStatus
                {
                    Id = 8,
                    NameAr = "معتمد",
                    NameEn = "Approved"
                },
                new ResolutionStatus
                {
                    Id = 9,
                    NameAr = "غير معتمد",
                    NameEn = "Not Approved"
                },
                new ResolutionStatus
                {
                    Id = 10,
                    NameAr = "ملغي",
                    NameEn = "Cancelled"
                }
            };
            foreach (var resolutionStatus in resolutionStatuses)
            {
                if (!await resolutionStatusRepository.AnyAsync<ResolutionStatus>(x => x.Id == resolutionStatus.Id))
                {
                    await resolutionStatusRepository.AddAsync(resolutionStatus);
                }
            }
        }
    }
}
