﻿using Abstraction.Contracts.Repository.Resolution;
using Domain.Entities.ResolutionManagement;

namespace Infrastructure.Data.Config
{

    public static class ResolutionStatusSeeds
    {
        public static async Task SeedResolutionStatusAsync(IResolutionStatusRepository resolutionStatusRepository)
        {
            var resolutionStatuses  =  new List<ResolutionStatus>
            {
                new ResolutionStatus
                {
                   
                    NameAr = "مسودة",
                    NameEn = "Draft"
                },
                new ResolutionStatus
                {
                    
                    NameAr = "معلق",
                    NameEn = "Pending"
                },
                new ResolutionStatus
                {
                    
                    NameAr = "استكمال البيانات",
                    NameEn = "Completing Data"
                },
                new ResolutionStatus
                {
                  
                    NameAr = "في انتظار التأكيد",
                    NameEn = "Waiting for Confirmation"
                },
                new ResolutionStatus
                {
                   
                    NameAr = "مؤكد",
                    NameEn = "Confirmed"
                },
                new ResolutionStatus
                {
                    
                    NameAr = "مرفوض",
                    NameEn = "Rejected"
                },
                new ResolutionStatus
                {
                   
                    NameAr = "التصويت قيد التقدم",
                    NameEn = "Voting in Progress"
                },
                new ResolutionStatus
                {
                   
                    NameAr = "معتمد",
                    NameEn = "Approved"
                },
                new ResolutionStatus
                {
                   
                    NameAr = "غير معتمد",
                    NameEn = "Not Approved"
                },
                new ResolutionStatus
                {
                    
                    NameAr = "ملغي",
                    NameEn = "Cancelled"
                }
            };
            foreach (var resolutionStatus in resolutionStatuses)
            {
                if (!await resolutionStatusRepository.AnyAsync<ResolutionStatus>(x => x.Id == resolutionStatus.Id))
                {
                    await resolutionStatusRepository.AddAsync(resolutionStatus);
                }
            }
        }
    }
}
