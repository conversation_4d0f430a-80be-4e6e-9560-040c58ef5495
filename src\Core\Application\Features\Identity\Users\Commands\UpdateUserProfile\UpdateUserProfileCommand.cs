using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Microsoft.AspNetCore.Http;
using Application.Features.Identity.Users.Dtos;

namespace Application.Features.Identity.Users.Commands.UpdateUserProfile
{
    /// <summary>
    /// Command to update user profile information
    /// Supports file uploads for CV and personal photo
    /// </summary>
    public record UpdateUserProfileCommand : BaseUserDto, ICommand<BaseResponse<string>>
    {

        public int? CvFileId { get; set; }

        /// <summary>
        /// Personal photo file ID (JPG/PNG, max 2MB)
        /// </summary>
        public int? PersonalPhotoFileId { get; set; }
    }
}
