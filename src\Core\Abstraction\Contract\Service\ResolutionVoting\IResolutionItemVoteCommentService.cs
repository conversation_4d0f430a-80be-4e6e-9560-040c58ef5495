using Abstraction.Contracts.Service;
using Domain.Entities.ResolutionManagement;

namespace Abstraction.Contract.Service.ResolutionVoting
{
    /// <summary>
    /// Service interface for ResolutionItemVoteComment operations
    /// Inherits from IBaseService to provide standard CRUD operations
    /// Follows the established service interface pattern in the project
    /// </summary>
    public interface IResolutionItemVoteCommentService : IBaseService<ResolutionItemVoteComment>
    {
        // Additional ResolutionItemVoteComment-specific methods can be added here if needed
        // For example:
        // Task<BaseResponse<IEnumerable<ResolutionItemVoteCommentDto>>> GetCommentsByResolutionItemAsync(int resolutionItemId);
        // Task<BaseResponse<IEnumerable<ResolutionItemVoteCommentDto>>> GetCommentsByBoardMemberAsync(int boardMemberId);
        // Task<BaseResponse<bool>> HasCommentsAsync(int resolutionItemId, int boardMemberId);
    }
}
