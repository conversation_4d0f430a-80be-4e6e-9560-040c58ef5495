using Domain.Entities.ResolutionManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Data.Config
{
    /// <summary>
    /// Entity Framework configuration for ResolutionMemberVoteStatus entity
    /// Configures table structure, relationships, and constraints
    /// </summary>
    public class ResolutionMemberVoteStatusConfig : IEntityTypeConfiguration<ResolutionMemberVoteStatus>
    {
        public void Configure(EntityTypeBuilder<ResolutionMemberVoteStatus> builder)
        {
            // Table configuration
            builder.ToTable("ResolutionMemberVoteStatuses");
            
            // Primary key
            builder.HasKey(x => x.Id);
            
            // Properties configuration
            builder.Property(x => x.NameAr)
                .IsRequired()
                .HasMaxLength(100)
                .HasComment("Status description for the vote status");

            builder.Property(x => x.NameEn)
               .IsRequired()
               .HasMaxLength(100)
               .HasComment("Status description for the vote status");
            
            
            // Audit fields configuration (inherited from FullAuditedEntity)
            builder.Property(x => x.CreatedAt)
                .IsRequired()
                .HasDefaultValueSql("GETUTCDATE()");
                
            builder.Property(x => x.UpdatedAt)
                .HasDefaultValueSql("GETUTCDATE()");
                
            builder.Property(x => x.IsDeleted)
                .HasDefaultValue(false);
                
            builder.Property(x => x.DeletedAt)
                .IsRequired(false);
            
            // Soft delete filter
            builder.HasQueryFilter(x => !x.IsDeleted.HasValue || !x.IsDeleted.Value);
        }
    }
}
