using Domain.Entities.ResolutionManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Data.Config
{
    /// <summary>
    /// Entity Framework configuration for ResolutionItemVoteComment entity
    /// Configures table structure, relationships, and constraints
    /// </summary>
    public class ResolutionItemVoteCommentConfig : IEntityTypeConfiguration<ResolutionItemVoteComment>
    {
        public void Configure(EntityTypeBuilder<ResolutionItemVoteComment> builder)
        {
            // Table configuration
            builder.ToTable("ResolutionItemVoteComments");
            
            // Primary key
            builder.HasKey(x => x.Id);
            
            // Properties configuration
            builder.Property(x => x.Comment)
                .IsRequired()
                .HasMaxLength(2000)
                .HasComment("Comment text providing additional information about the item vote");

            builder.Property(x => x.ResolutionItemID)
                .IsRequired()
                .HasComment("Foreign key reference to ResolutionItem entity");

            builder.Property(x => x.BoardMemberID)
                .IsRequired()
                .HasComment("Foreign key reference to BoardMember entity");

            // Relationships configuration
            builder.HasOne(x => x.ResolutionItem)
                .WithMany(i => i.ResolutionItemVoteComments)
                .HasForeignKey(x => x.ResolutionItemID)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_ResolutionItemVoteComments_ResolutionItems");

            builder.HasOne(x => x.BoardMember)
                .WithMany(b => b.ResolutionItemVoteComments)
                .HasForeignKey(x => x.BoardMemberID)
                .OnDelete(DeleteBehavior.Restrict)
                .HasConstraintName("FK_ResolutionItemVoteComments_BoardMembers");

            // Indexes for performance
            builder.HasIndex(x => x.ResolutionItemID)
                .HasDatabaseName("IX_ResolutionItemVoteComments_ResolutionItemId");

            builder.HasIndex(x => x.BoardMemberID)
                .HasDatabaseName("IX_ResolutionItemVoteComments_BoardMemberId");

            // Composite index for efficient querying by item and member
            builder.HasIndex(x => new { x.ResolutionItemID, x.BoardMemberID })
                .HasDatabaseName("IX_ResolutionItemVoteComments_ItemId_MemberId");
                
            builder.HasIndex(x => x.CreatedAt)
                .HasDatabaseName("IX_ResolutionItemVoteComments_CreatedAt");
            
            // Audit fields configuration (inherited from FullAuditedEntity)
            builder.Property(x => x.CreatedAt)
                .IsRequired()
                .HasDefaultValueSql("GETUTCDATE()");
                
            builder.Property(x => x.UpdatedAt)
                .HasDefaultValueSql("GETUTCDATE()");
                
            builder.Property(x => x.IsDeleted)
                .HasDefaultValue(false);
                
            builder.Property(x => x.DeletedAt)
                .IsRequired(false);
            
            // Soft delete filter
            builder.HasQueryFilter(x => !x.IsDeleted.HasValue || !x.IsDeleted.Value);
        }
    }
}
