using Domain.Entities.Base;
using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Entities.ResolutionManagement
{
    /// <summary>
    /// Represents the history of status changes for a resolution member vote
    /// Inherits from FullAuditedEntity to provide comprehensive audit trail functionality
    /// Tracks the progression of vote statuses over time
    /// </summary>
    public class ResolutionMemberVoteStatusHistory : FullAuditedEntity
    {
        /// <summary>
        /// Status identifier for this history record
        /// Foreign key reference to ResolutionMemberVoteStatus entity
        /// </summary>
        public int StatusID { get; set; }

        /// <summary>
        /// Resolution member vote identifier that this history belongs to
        /// Foreign key reference to ResolutionMemberVote entity
        /// </summary>
        public int ResolutionMemberVoteID { get; set; }

        /// <summary>
        /// Navigation property to ResolutionMemberVoteStatus entity
        /// Provides access to the status information
        /// </summary>
        [ForeignKey("StatusID")]
        public virtual ResolutionMemberVoteStatus Status { get; set; } = null!;

        /// <summary>
        /// Navigation property to ResolutionMemberVote entity
        /// Provides access to the vote this history record belongs to
        /// </summary>
        [ForeignKey("ResolutionMemberVoteID")]
        public virtual ResolutionMemberVote ResolutionMemberVote { get; set; } = null!;

        
    }
}
