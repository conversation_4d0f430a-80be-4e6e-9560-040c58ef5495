using Domain.Entities.ResolutionManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Data.Config
{
    /// <summary>
    /// Entity Framework configuration for ResolutionMemberVoteComment entity
    /// Configures table structure, relationships, and constraints
    /// </summary>
    public class ResolutionMemberVoteCommentConfig : IEntityTypeConfiguration<ResolutionMemberVoteComment>
    {
        public void Configure(EntityTypeBuilder<ResolutionMemberVoteComment> builder)
        {
            // Table configuration
            builder.ToTable("ResolutionMemberVoteComments");
            
            // Primary key
            builder.HasKey(x => x.Id);
            
            // Properties configuration
            builder.Property(x => x.ResolutionMemberVoteID)
                .IsRequired()
                .HasComment("Foreign key reference to ResolutionMemberVote entity");
                
            builder.Property(x => x.Comment)
                .IsRequired()
                .HasMaxLength(2000)
                .HasComment("Comment text providing additional information about the vote");
            
            // Relationships configuration
            builder.HasOne(x => x.ResolutionMemberVote)
                .WithMany(v => v.ResolutionMemberVoteComments)
                .HasForeignKey(x => x.ResolutionMemberVoteID)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_ResolutionMemberVoteComments_Votes");
            
            // Indexes for performance
            builder.HasIndex(x => x.ResolutionMemberVoteID)
                .HasDatabaseName("IX_ResolutionMemberVoteComments_VoteId");
                
            builder.HasIndex(x => x.CreatedAt)
                .HasDatabaseName("IX_ResolutionMemberVoteComments_CreatedAt");
            
            // Audit fields configuration (inherited from FullAuditedEntity)
            builder.Property(x => x.CreatedAt)
                .IsRequired()
                .HasDefaultValueSql("GETUTCDATE()");
                
            builder.Property(x => x.UpdatedAt)
                .HasDefaultValueSql("GETUTCDATE()");
                
            builder.Property(x => x.IsDeleted)
                .HasDefaultValue(false);
                
            builder.Property(x => x.DeletedAt)
                .IsRequired(false);
            
            // Soft delete filter
            builder.HasQueryFilter(x => !x.IsDeleted.HasValue || !x.IsDeleted.Value);
        }
    }
}
