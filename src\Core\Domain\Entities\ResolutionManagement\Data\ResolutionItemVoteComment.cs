using Domain.Entities.Base;
using Domain.Entities.FundManagement;
using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Entities.ResolutionManagement
{
    /// <summary>
    /// Represents a comment associated with a specific resolution item by a board member
    /// Inherits from FullAuditedEntity to provide comprehensive audit trail functionality
    /// Allows board members to provide item-specific explanations or notes about their votes
    /// </summary>
    public class ResolutionItemVoteComment : FullAuditedEntity
    {
        /// <summary>
        /// Comment text providing additional information about the item vote
        /// Can be used to explain the reasoning behind the vote decision for this specific item
        /// </summary>
        public string Comment { get; set; } = string.Empty;

        /// <summary>
        /// Resolution item identifier that this comment belongs to
        /// Foreign key reference to ResolutionItem entity
        /// </summary>
        public int ResolutionItemID { get; set; }

        /// <summary>
        /// Board member identifier who made this comment
        /// Foreign key reference to BoardMember entity
        /// </summary>
        public int BoardMemberID { get; set; }

        /// <summary>
        /// Navigation property to ResolutionItem entity
        /// Provides access to the resolution item this comment belongs to
        /// </summary>
        [ForeignKey("ResolutionItemID")]
        public virtual ResolutionItem ResolutionItem { get; set; } = null!;

        /// <summary>
        /// Navigation property to BoardMember entity
        /// Provides access to the board member who made this comment
        /// </summary>
        [ForeignKey("BoardMemberID")]
        public virtual BoardMember BoardMember { get; set; } = null!;

    }
}
