﻿using Abstraction.Contracts.Repository.Resolution;
using Domain.Entities.ResolutionManagement;

namespace Infrastructure.Data.Config
{

    public static class ResolutionTypeSeed
    {
        public static async Task SeedResolutionTypeAsync(IResolutionTypeRepository resolutionTypeRepository)
        {
            var resolutionTypes = new List<ResolutionType>
            {
                new ResolutionType
                {
                   
                    NameAr = "استحواذ",
                    NameEn = "Acquisition",
                    IsActive = true,
                    IsOther = false,
                    DisplayOrder = 1,
                   
                },
                new ResolutionType
                {
                   
                    NameAr = "تخارج",
                    NameEn = "Exit",
                    IsActive = true,
                    IsOther = false,
                    DisplayOrder = 2,
                   
                },
                new ResolutionType
                {
                  
                    NameAr = "بيع",
                    NameEn = "Sell",
                    IsActive = true,
                    IsOther = false,
                    DisplayOrder = 3,
                   
                },
                new ResolutionType
                {
                   
                    NameAr = "توزيع أرباح",
                    NameEn = "ProfitDistribution",
                    IsActive = true,
                    IsOther = false,
                    DisplayOrder = 4,
                   
                },
                new ResolutionType
                {
                  
                    NameAr = "تمديد مدة الصندوق",
                    NameEn = "FundDurationExtension",
                    IsActive = true,
                    IsOther = false,
                    DisplayOrder = 5,
                  
                },
                new ResolutionType
                {
                  
                    NameAr = "تعديل شروط وأحكام الصندوق",
                    NameEn = "TermsAndConditionsModification",
                    IsActive = true,
                    IsOther = false,
                    DisplayOrder = 6,
                   
                },
                new ResolutionType
                {
                  
                    NameAr = "الموافقة على القوائم المالية",
                    NameEn = "FinancialStatementsApproval",
                    IsActive = true,
                    IsOther = false,
                    DisplayOrder = 7,
                    
                 
                },
                new ResolutionType
                {
                   
                    NameAr = "تعيين مقدمي خدمات",
                    NameEn = "ServiceProviderAssignment",
                    IsActive = true,
                    IsOther = false,
                    DisplayOrder = 8,
                   
                  
                },
                new ResolutionType
                {
                  
                    NameAr = "الموافقة على شروط وأحكام الصندوق",
                    NameEn = "TermsAndConditionsApproval",
                    IsActive = true,
                    IsOther = false,
                    DisplayOrder = 9,
                  
                },
                new ResolutionType
                {
                   
                    NameAr = "أخرى",
                    NameEn = "Other",
                    IsActive = true,
                    IsOther = true,
                    DisplayOrder = 10
                   
                }
            };
            foreach (var resolutionType in resolutionTypes)
            {
                if (!await resolutionTypeRepository.AnyAsync<ResolutionType>(x => x.Id == resolutionType.Id))
                {
                    await resolutionTypeRepository.AddAsync(resolutionType);
                }
            }

        }
    }
}

