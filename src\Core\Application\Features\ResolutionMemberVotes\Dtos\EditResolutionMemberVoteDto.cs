﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Abstraction.Base.Dto;
using Domain.Entities.ResolutionManagement;

namespace Application.Features.ResolutionMemberVotes.Dtos
{
    public record EditResolutionMemberVoteDto : BaseDto
    {
        /// <summary>
        /// Resolution identifier that this vote is for
        /// Foreign key reference to Resolution  entity
        /// </summary>
        public int ResolutionId { get; set; }
        public List<EditResolutionItemVoteDto>? ItemsVote { get; set; }
        public List<EditResolutionVoteCommentDto>? VoteComments { get; set; }
        public int CreatedBy { get; set; } // ID of the user who created this vote
        public DateTime CreatedAt { get; set; } // Timestamp of when the vote was created
    }
    public record EditResolutionItemVoteDto : BaseDto
    {
        /// <summary>
        /// Resolution item identifier that this vote is for
        /// Foreign key reference to ResolutionItem entity
        /// </summary>
        public int ResolutionItemId { get; set; }

        /// <summary>
        /// The vote result for this specific resolution item
        /// Uses VoteResult enum (NotEligibleToVote = 0 , NotVotedYet = 1, Accept = 2, Reject = 3)
        /// </summary>
        public VoteResult VoteResult { get; set; }
        public List<EditResolutionItemVoteCommentDto>? ItemComments { get; set; }
        public int CreatedBy { get; set; } // ID of the user who created this vote
        public DateTime CreatedAt { get; set; } // Timestamp of when the vote was created
     

    }
    public record EditResolutionItemVoteCommentDto : BaseDto
    {
        /// <summary>
        /// Comment text providing additional information about the item vote
        /// Can be used to explain the reasoning behind the vote decision for this specific item
        /// </summary>
        public string? Comment { get; set; }
        public int CreatedBy { get; set; } // ID of the user who created this vote
        public DateTime CreatedAt { get; set; } // Timestamp of when the vote was created
         
    }
    public record EditResolutionVoteCommentDto : BaseDto
    {
        /// <summary>
        /// Comment text providing additional information about the item vote
        /// Can be used to explain the reasoning behind the vote decision for this specific item
        /// </summary>
        public string? Comment { get; set; }
        public int CreatedBy { get; set; } // ID of the user who created this vote
        public DateTime CreatedAt { get; set; } // Timestamp of when the vote was created
        
    }
}
