using FluentValidation;
using Microsoft.Extensions.Localization;
using Resources;
using Microsoft.AspNetCore.Http;
using Abstraction.Contracts.Repository;
using Domain.Entities.Shared;

namespace Application.Features.Identity.Users.Commands.UpdateUserProfile
{
    /// <summary>
    /// Validator for UpdateUserProfileCommand
    /// Implements Sprint 3 validation rules with localization
    /// </summary>
    public class UpdateUserProfileCommandValidator : AbstractValidator<UpdateUserProfileCommand>
    {
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly IRepositoryManager _repository;
        public UpdateUserProfileCommandValidator(IStringLocalizer<SharedResources> localizer, IRepositoryManager repository)
        {
            _localizer = localizer;
            _repository = repository;
            ApplyValidationRules();
        }

        private void ApplyValidationRules()
        {
            // Email validation
            RuleFor(x => x.Email)
                .NotEmpty()
                .WithMessage(_localizer[SharedResourcesKey.ProfileRequiredField])
                .EmailAddress()
                .WithMessage(_localizer[SharedResourcesKey.ProfileInvalidEmailFormat])
                .MaximumLength(255)
                .WithMessage(_localizer[SharedResourcesKey.MaxLength, 255]);

          

            // IBAN validation (optional)
            RuleFor(x => x.IBAN)
                .MaximumLength(34)
                .WithMessage(_localizer[SharedResourcesKey.MaxLength, 34])
                .Must(BeValidIBAN)
                .WithMessage(_localizer[SharedResourcesKey.InvalidIBANFormat])
                .When(x => !string.IsNullOrWhiteSpace(x.IBAN));

            // Nationality validation
            RuleFor(x => x.Nationality)
                .MaximumLength(100)
                .WithMessage(_localizer[SharedResourcesKey.MaxLength, 100])
                .When(x => !string.IsNullOrWhiteSpace(x.Nationality));

            // Passport number validation
            RuleFor(x => x.PassportNo)
                .MaximumLength(20)
                .WithMessage(_localizer[SharedResourcesKey.MaxLength, 20])
                .Matches(@"^[A-Za-z0-9]+$")
                .WithMessage(_localizer[SharedResourcesKey.PassportNumberAlphanumeric])
                .When(x => !string.IsNullOrWhiteSpace(x.PassportNo));

            // CV file validation
            RuleFor(x => x.CvFileId)
                .MustAsync(async (cvFileId, cancellation) =>
                {
                    if (cvFileId <= 0) return true; // Optional attachment

                    var attachment = await _repository.Attachments.GetByIdAsync<Attachment>(cvFileId.Value, trackChanges: false);
                    if (attachment == null) return true; // Let attachment existence validation handle this

                    return attachment.FileName.EndsWith(".pdf", StringComparison.OrdinalIgnoreCase) ||
                            attachment.FileName.EndsWith(".doc", StringComparison.OrdinalIgnoreCase) ||
                            attachment.FileName.EndsWith(".docx", StringComparison.OrdinalIgnoreCase);
                })
                .WithMessage(_localizer[SharedResourcesKey.InvalidFileType])
                .When(x => x.CvFileId > 0);

            // Business rule: Attachment file size must not exceed 10 MB (Sprint.md requirement)
            RuleFor(x => x.CvFileId)
                .MustAsync(async (cvFileId, cancellation) =>
                {
                    if (cvFileId <= 0) return true; // Optional attachment

                    var attachment = await _repository.Attachments.GetByIdAsync<Attachment>(cvFileId.Value, trackChanges: false);
                    if (attachment == null) return true; // Let attachment existence validation handle this

                    // Check file size - 10 MB = 10 * 1024 * 1024 bytes
                    const long maxFileSizeBytes = 10 * 1024 * 1024;
                    return attachment.FileSize <= maxFileSizeBytes;
                })
                .WithMessage(_localizer[SharedResourcesKey.FileSizeExceedsLimit])
                .When(x => x.CvFileId > 0);

          
            // Personal photo file validation
            RuleFor(x => x.PersonalPhotoFileId)
                .MustAsync(async (personalPhotoFileId, cancellation) =>
                {
                    if (personalPhotoFileId <= 0) return true; // Optional attachment

                    var attachment = await _repository.Attachments.GetByIdAsync<Attachment>(personalPhotoFileId.Value, trackChanges: false);
                    if (attachment == null) return true; // Let attachment existence validation handle this

                    return attachment.FileName.EndsWith(".jpg", StringComparison.OrdinalIgnoreCase) ||
                            attachment.FileName.EndsWith(".jpeg", StringComparison.OrdinalIgnoreCase) ||
                            attachment.FileName.EndsWith(".png", StringComparison.OrdinalIgnoreCase);
                })
                .WithMessage(_localizer[SharedResourcesKey.InvalidFileType])
                .When(x => x.PersonalPhotoFileId > 0);

            // Business rule: Personal photo file size must not exceed 2 MB
            RuleFor(x => x.PersonalPhotoFileId)
                .MustAsync(async (personalPhotoFileId, cancellation) =>
                {
                    if (personalPhotoFileId <= 0) return true; // Optional attachment

                    var attachment = await _repository.Attachments.GetByIdAsync<Attachment>(personalPhotoFileId.Value, trackChanges: false);
                    if (attachment == null) return true; // Let attachment existence validation handle this

                    // Check file size - 2 MB = 2 * 1024 * 1024 bytes
                    const long maxFileSizeBytes = 2 * 1024 * 1024;
                    return attachment.FileSize <= maxFileSizeBytes;
                })
                .WithMessage(_localizer[SharedResourcesKey.FileSizeExceedsLimit])
                .When(x => x.PersonalPhotoFileId > 0);


            //// CV file validation
            //RuleFor(x => x.CVFile)
            //    .Must(BeValidCVFile)
            //    .WithMessage(_localizer[SharedResourcesKey.ProfileInvalidCVFile])
            //    .When(x => x.CVFile != null);

            //// Personal photo validation
            //RuleFor(x => x.PersonalPhoto)
            //    .Must(BeValidPhotoFile)
            //    .WithMessage(_localizer[SharedResourcesKey.ProfileInvalidPhotoFile])
            //    .When(x => x.PersonalPhoto != null);


        }

        private bool BeValidIBAN(string? iban)
        {
            if (string.IsNullOrWhiteSpace(iban))
                return true;

            // Basic IBAN validation (simplified)
            iban = iban.Replace(" ", "").ToUpperInvariant();
            return iban.Length >= 15 && iban.Length <= 34 && iban.All(c => char.IsLetterOrDigit(c));
        }

        private bool BeValidCVFile(IFormFile? file)
        {
            if (file == null)
                return true;

            var allowedExtensions = new[] { ".pdf", ".doc", ".docx" };
            var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
            var maxSizeInBytes = 10 * 1024 * 1024; // 10MB

            return allowedExtensions.Contains(extension) && file.Length <= maxSizeInBytes;
        }

        private bool BeValidPhotoFile(IFormFile? file)
        {
            if (file == null)
                return true;

            var allowedExtensions = new[] { ".jpg", ".jpeg", ".png" };
            var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
            var maxSizeInBytes = 2 * 1024 * 1024; // 2MB

            return allowedExtensions.Contains(extension) && file.Length <= maxSizeInBytes;
        }
    }
}
