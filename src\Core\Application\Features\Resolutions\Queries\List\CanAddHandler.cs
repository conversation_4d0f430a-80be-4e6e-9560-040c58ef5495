﻿using Abstraction.Base.Response;
using Abstraction.Common.Wappers;
using Abstraction.Contract.Service;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Application.Base.Abstracts;
using Application.Features.Resolutions.Dtos;
using AutoMapper;
using Domain.Entities.ResolutionManagement;
using Domain.Services;
using Microsoft.Extensions.Localization;
using Resources;
using Abstraction.Constants;
using Abstraction.Base.Dto;
using Domain.Helpers;
using Application.Features.Resolutions.Queries.List;
namespace Application.Features.Resolutions.Queries.List
{
    /// <summary>
    /// Handler for ListQuery to retrieve paginated resolution list
    /// Implements role-based access control and comprehensive filtering
    /// Based on Sprint.md requirements (JDWA-582)
    /// </summary>
    /// 
    public record CheckAddPermission : IQuery<BaseResponse<ResolutionPermissions>>
    {
        public int Id { get; set; }
    }
    public record ResolutionPermissions 
    {
        public bool CanAdd { get; set; }
    }
    public class CanAddHandler : BaseResponseHandler, IQueryHandler<CheckAddPermission, BaseResponse<ResolutionPermissions>>
    {
        #region Fields
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _repository;
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly ICurrentUserService _currentUserService;
        #endregion

        #region Constructor(s)
        public CanAddHandler(
            IRepositoryManager repository,
            ILoggerManager logger,
            IStringLocalizer<SharedResources> localizer,
            ICurrentUserService currentUserService)
        {
            _logger = logger;
            _repository = repository;
            _localizer = localizer;
            _currentUserService = currentUserService;
        }
        #endregion

        #region Functions
        public async Task<BaseResponse<ResolutionPermissions>> Handle(CheckAddPermission request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInfo("Starting GetResolutionsList operation");

                // 1. Get current user information for role-based filtering
                var currentUserId = _currentUserService.UserId;
                var userRole = await GetUserFundRole(request.Id, _currentUserService.UserId.Value);
                var pewrmissions = new ResolutionPermissions();
                pewrmissions.CanAdd = userRole == Roles.FundManager;
                return Success(pewrmissions);
                // Specify the type argument explicitly to resolve CS0411  

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetResolutionsList");
                return ServerError<ResolutionPermissions>(_localizer[SharedResourcesKey.SystemErrorRetrievingData]);
            }
        }

        /// <summary>
        /// Determines the current user's role within a specific fund context
        /// Checks FundBoardSecretary table, Fund.LegalCouncilId field, and FundManager table
        /// Follows Clean Architecture and CQRS patterns with proper repository usage
        /// </summary>
        /// <param name="fundId">The fund ID to check roles for</param>
        /// <param name="currentUserId">The current user ID to check roles for</param>
        /// <returns>Comma-separated string of roles the user has in the fund, or empty string if no roles</returns>
        private async Task<Roles> GetUserFundRole(int fundId, int currentUserId)
        {
            try
            {
                _logger.LogInfo($"Checking fund roles for User ID: {currentUserId} in Fund ID: {fundId}");

                var userRole = Roles.None;
                // Get fund details with all related entities
                var fundDetails = await _repository.Funds.ViewFundUsers(fundId, trackChanges: false);
                if (fundDetails == null)
                {
                    _logger.LogWarn($"Fund not found with ID: {fundId}");
                    return Roles.None;
                }

                // 1. Check if user is Legal Council for the fund
                if (fundDetails.LegalCouncilId == currentUserId)
                {
                    userRole = Roles.LegalCouncil;
                    _logger.LogInfo($"User ID: {currentUserId} is Legal Council for Fund ID: {fundId}");
                }

                // 2. Check if user is a Fund Manager for the fund
                if (fundDetails.FundManagers != null && fundDetails.FundManagers.Count > 0)
                {
                    var isFundManager = fundDetails.FundManagers.Any(fm => fm.UserId == currentUserId);
                    if (isFundManager)
                    {
                        userRole = Roles.FundManager;
                        _logger.LogInfo($"User ID: {currentUserId} is Fund Manager for Fund ID: {fundId}");
                    }
                }

                // 3. Check if user is a Board Secretary for the fund
                if (fundDetails.FundBoardSecretaries != null && fundDetails.FundBoardSecretaries.Count > 0)
                {
                    var isBoardSecretary = fundDetails.FundBoardSecretaries.Any(bs => bs.UserId == currentUserId);
                    if (isBoardSecretary)
                    {
                        userRole = Roles.BoardSecretary;
                        _logger.LogInfo($"User ID: {currentUserId} is Board Secretary for Fund ID: {fundId}");
                    }
                }

                // Return comma-separated roles or empty string if no roles found

                _logger.LogInfo($"User ID: {currentUserId} has roles in Fund ID: {fundId}: '{userRole}'");

                return userRole;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error checking fund roles for User ID: {currentUserId} in Fund ID: {fundId}");
                return Roles.None;
            }
        }
 

        #endregion
    }
}

