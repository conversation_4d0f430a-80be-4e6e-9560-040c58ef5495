using Domain.Entities.ResolutionManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Data.Config
{
    /// <summary>
    /// Entity Framework configuration for ResolutionMemberVote entity
    /// Configures table structure, relationships, and constraints
    /// </summary>
    public class ResolutionMemberVoteConfig : IEntityTypeConfiguration<ResolutionMemberVote>
    {
        public void Configure(EntityTypeBuilder<ResolutionMemberVote> builder)
        {
            // Table configuration
            builder.ToTable("ResolutionMemberVotes");
            
            // Primary key
            builder.HasKey(x => x.Id);
            
            // Properties configuration
            builder.Property(x => x.ResolutionId)
                .IsRequired()
                .HasComment("Foreign key reference to Resolution entity");
                
            builder.Property(x => x.BoardMemberID)
                .IsRequired()
                .HasComment("Foreign key reference to BoardMember entity");
                
            builder.Property(x => x.VoteResult)
                .IsRequired()
                .HasConversion<int>()
                .HasDefaultValue(VoteResult.NotVotedYet)
                .HasComment("Vote result (NotVotedYet=0, Accept=1, Reject=2)");
            
            // Relationships configuration
            builder.HasOne(x => x.Resolution)
                .WithMany(r => r.ResolutionMemberVotes)
                .HasForeignKey(x => x.ResolutionId)
                .OnDelete(DeleteBehavior.Restrict)
                .HasConstraintName("FK_ResolutionMemberVotes_Resolutions");

            builder.HasOne(x => x.BoardMember)
                .WithMany(b => b.ResolutionMemberVotes)
                .HasForeignKey(x => x.BoardMemberID)
                .OnDelete(DeleteBehavior.Restrict)
                .HasConstraintName("FK_ResolutionMemberVotes_BoardMembers");
            
            // Indexes for performance
            builder.HasIndex(x => x.ResolutionId)
                .HasDatabaseName("IX_ResolutionMemberVotes_ResolutionId");
                
            builder.HasIndex(x => x.BoardMemberID)
                .HasDatabaseName("IX_ResolutionMemberVotes_BoardMemberId");
                
            builder.HasIndex(x => new { x.ResolutionId, x.BoardMemberID })
                .HasFilter("[IsDeleted] = 0")
                .IsUnique()
                .HasDatabaseName("IX_ResolutionMemberVotes_Resolution_Member_Unique");
                
            builder.HasIndex(x => x.VoteResult)
                .HasDatabaseName("IX_ResolutionMemberVotes_VoteResult");
            
            // Audit fields configuration (inherited from FullAuditedEntity)
            builder.Property(x => x.CreatedAt)
                .IsRequired()
                .HasDefaultValueSql("GETUTCDATE()");
                
            builder.Property(x => x.UpdatedAt)
                .HasDefaultValueSql("GETUTCDATE()");
                
            builder.Property(x => x.IsDeleted)
                .HasDefaultValue(false);
                
            builder.Property(x => x.DeletedAt)
                .IsRequired(false);
            
            // Soft delete filter
            builder.HasQueryFilter(x => !x.IsDeleted.HasValue || !x.IsDeleted.Value);
        }
    }
}
