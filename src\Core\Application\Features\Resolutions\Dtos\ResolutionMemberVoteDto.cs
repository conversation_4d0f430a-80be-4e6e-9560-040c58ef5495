using Domain.Entities.ResolutionManagement;
using System.ComponentModel.DataAnnotations;

namespace Application.Features.Resolutions.Dtos
{
    /// <summary>
    /// Data Transfer Object for ResolutionMemberVote entity
    /// Used for API operations and data transfer between layers
    /// Based on requirements for resolution member voting system
    /// </summary>
    public record ResolutionMemberVoteDto
    {
        /// <summary>
        /// Unique identifier for the resolution member vote
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Resolution identifier that this vote belongs to
        /// Required field for API operations
        /// </summary>
        [Display(Name = "Resolution ID")]
        public int ResolutionId { get; set; }

        /// <summary>
        /// Board member identifier who cast this vote
        /// Required field for API operations
        /// </summary>
        [Display(Name = "Board Member ID")]
        public int BoardMemberID { get; set; }

        /// <summary>
        /// The vote result (NotVotedYet = 0, Accept = 1, Reject = 2)
        /// Represents the member's voting decision
        /// </summary>
        [Display(Name = "Vote Result")]
        public VoteResult VoteResult { get; set; } = VoteResult.NotVotedYet;

        /// <summary>
        /// Indicates whether the member has voted
        /// Computed property for display purposes
        /// </summary>
        public bool HasVoted => VoteResult != VoteResult.NotVotedYet;

        /// <summary>
        /// Board member name for display purposes
        /// </summary>
        public string? BoardMemberName { get; set; }

        /// <summary>
        /// User name for display purposes
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// User full name for display purposes
        /// </summary>
        public string? UserFullName { get; set; }

        /// <summary>
        /// Member type for display purposes
        /// </summary>
        public string? MemberType { get; set; }

        /// <summary>
        /// Vote value display text (localized)
        /// </summary>
        public string? VoteValueDisplay { get; set; }

        /// <summary>
        /// When the vote was cast
        /// </summary>
        public DateTime? VotedAt { get; set; }

        /// <summary>
        /// Whether the member is chairman
        /// </summary>
        public bool IsChairman { get; set; }
    }
}
