using Application.Features.Identity.Users.Dtos;
using Application.Features.Resolutions.Dtos;
using Domain.Entities.Shared;
using Domain.Entities.Users;

namespace Application.Mapping.Users
{
    public partial class UserProfile
    {
        public void GetUserProfileMapping()
        {
            CreateMap<User, UserProfileResponseDto>()
                .ForMember(dest => dest.LastUpdateDate, opt => opt.MapFrom(c=> c.UpdatedAt.HasValue ? c.UpdatedAt : null ))
                .ForMember(dest => dest.Roles, opt => opt.MapFrom(c => c.Roles.Select(r => new RolesDto
                {
                    Id = r.Id.ToString(),
                    Name = r.Name,
                    Value = r.Name
                }).ToList()));

            CreateMap<Role, RolesDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id.ToString()))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.Value, opt => opt.MapFrom(src => src.Name));
        }
    }
}
