using Abstraction.Contracts.Service;
using Domain.Entities.ResolutionManagement;

namespace Abstraction.Contract.Service.ResolutionVoting
{
    /// <summary>
    /// Service interface for ResolutionMemberVoteComment operations
    /// Inherits from IBaseService to provide standard CRUD operations
    /// Follows the established service interface pattern in the project
    /// </summary>
    public interface IResolutionMemberVoteCommentService : IBaseService<ResolutionMemberVoteComment>
    {
        // Additional ResolutionMemberVoteComment-specific methods can be added here if needed
        // For example:
        // Task<BaseResponse<IEnumerable<ResolutionMemberVoteCommentDto>>> GetCommentsByVoteAsync(int resolutionMemberVoteId);
        // Task<BaseResponse<bool>> HasCommentsAsync(int resolutionMemberVoteId);
    }
}
