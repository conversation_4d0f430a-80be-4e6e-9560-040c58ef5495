using Domain.Entities.Base;

namespace Domain.Entities.ResolutionManagement
{
    /// <summary>
    /// Represents a status for resolution member votes
    /// Inherits from FullAuditedEntity to provide comprehensive audit trail functionality
    /// Used to track different states of member voting process
    /// </summary>
    public class ResolutionMemberVoteStatus : FullAuditedEntity
    {
        public string NameAr { get; set; }
        public string NameEn { get; set; }
    }
}
