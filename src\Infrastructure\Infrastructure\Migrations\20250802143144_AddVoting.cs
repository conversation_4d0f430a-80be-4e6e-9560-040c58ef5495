﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddVoting : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ResolutionVotes");

            migrationBuilder.CreateTable(
                name: "ResolutionItemVoteComments",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Comment = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: false, comment: "Comment text providing additional information about the item vote"),
                    ResolutionItemID = table.Column<int>(type: "int", nullable: false, comment: "Foreign key reference to ResolutionItem entity"),
                    BoardMemberID = table.Column<int>(type: "int", nullable: false, comment: "Foreign key reference to BoardMember entity"),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    CreatedBy = table.Column<int>(type: "int", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true, defaultValueSql: "GETUTCDATE()"),
                    UpdatedBy = table.Column<int>(type: "int", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: true, defaultValue: false),
                    DeletedBy = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ResolutionItemVoteComments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ResolutionItemVoteComments_AspNetUsers_CreatedBy",
                        column: x => x.CreatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ResolutionItemVoteComments_AspNetUsers_DeletedBy",
                        column: x => x.DeletedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ResolutionItemVoteComments_AspNetUsers_UpdatedBy",
                        column: x => x.UpdatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ResolutionItemVoteComments_BoardMembers",
                        column: x => x.BoardMemberID,
                        principalTable: "BoardMembers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ResolutionItemVoteComments_ResolutionItems",
                        column: x => x.ResolutionItemID,
                        principalTable: "ResolutionItems",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ResolutionMemberVotes",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ResolutionId = table.Column<int>(type: "int", nullable: false, comment: "Foreign key reference to Resolution entity"),
                    BoardMemberID = table.Column<int>(type: "int", nullable: false, comment: "Foreign key reference to BoardMember entity"),
                    VoteResult = table.Column<int>(type: "int", nullable: false, defaultValue: 1, comment: "Vote result (NotVotedYet=0, Accept=1, Reject=2)"),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    CreatedBy = table.Column<int>(type: "int", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true, defaultValueSql: "GETUTCDATE()"),
                    UpdatedBy = table.Column<int>(type: "int", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: true, defaultValue: false),
                    DeletedBy = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ResolutionMemberVotes", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ResolutionMemberVotes_AspNetUsers_CreatedBy",
                        column: x => x.CreatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ResolutionMemberVotes_AspNetUsers_DeletedBy",
                        column: x => x.DeletedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ResolutionMemberVotes_AspNetUsers_UpdatedBy",
                        column: x => x.UpdatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ResolutionMemberVotes_BoardMembers",
                        column: x => x.BoardMemberID,
                        principalTable: "BoardMembers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ResolutionMemberVotes_Resolutions",
                        column: x => x.ResolutionId,
                        principalTable: "Resolutions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "ResolutionMemberVoteStatuses",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    NameAr = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, comment: "Status description for the vote status"),
                    NameEn = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, comment: "Status description for the vote status"),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    CreatedBy = table.Column<int>(type: "int", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true, defaultValueSql: "GETUTCDATE()"),
                    UpdatedBy = table.Column<int>(type: "int", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: true, defaultValue: false),
                    DeletedBy = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ResolutionMemberVoteStatuses", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ResolutionMemberVoteStatuses_AspNetUsers_CreatedBy",
                        column: x => x.CreatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ResolutionMemberVoteStatuses_AspNetUsers_DeletedBy",
                        column: x => x.DeletedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ResolutionMemberVoteStatuses_AspNetUsers_UpdatedBy",
                        column: x => x.UpdatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "ResolutionItemVotes",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ResolutionItemId = table.Column<int>(type: "int", nullable: false, comment: "Foreign key reference to ResolutionItem entity"),
                    ResolutionMemberVoteId = table.Column<int>(type: "int", nullable: false, comment: "Foreign key reference to ResolutionMemberVote entity"),
                    VoteResult = table.Column<int>(type: "int", nullable: false, defaultValue: 1, comment: "Vote result for this specific resolution item (NotVotedYet=0, Accept=1, Reject=2)"),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    CreatedBy = table.Column<int>(type: "int", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true, defaultValueSql: "GETUTCDATE()"),
                    UpdatedBy = table.Column<int>(type: "int", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: true, defaultValue: false),
                    DeletedBy = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ResolutionItemVotes", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ResolutionItemVotes_AspNetUsers_CreatedBy",
                        column: x => x.CreatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ResolutionItemVotes_AspNetUsers_DeletedBy",
                        column: x => x.DeletedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ResolutionItemVotes_AspNetUsers_UpdatedBy",
                        column: x => x.UpdatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ResolutionItemVotes_MemberVotes",
                        column: x => x.ResolutionMemberVoteId,
                        principalTable: "ResolutionMemberVotes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ResolutionItemVotes_ResolutionItems",
                        column: x => x.ResolutionItemId,
                        principalTable: "ResolutionItems",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "ResolutionMemberVoteComments",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ResolutionMemberVoteID = table.Column<int>(type: "int", nullable: false, comment: "Foreign key reference to ResolutionMemberVote entity"),
                    Comment = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: false, comment: "Comment text providing additional information about the vote"),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    CreatedBy = table.Column<int>(type: "int", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true, defaultValueSql: "GETUTCDATE()"),
                    UpdatedBy = table.Column<int>(type: "int", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: true, defaultValue: false),
                    DeletedBy = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ResolutionMemberVoteComments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ResolutionMemberVoteComments_AspNetUsers_CreatedBy",
                        column: x => x.CreatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ResolutionMemberVoteComments_AspNetUsers_DeletedBy",
                        column: x => x.DeletedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ResolutionMemberVoteComments_AspNetUsers_UpdatedBy",
                        column: x => x.UpdatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ResolutionMemberVoteComments_Votes",
                        column: x => x.ResolutionMemberVoteID,
                        principalTable: "ResolutionMemberVotes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ResolutionMemberVoteStatusHistories",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    StatusID = table.Column<int>(type: "int", nullable: false, comment: "Foreign key reference to ResolutionMemberVoteStatus entity"),
                    ResolutionMemberVoteID = table.Column<int>(type: "int", nullable: false, comment: "Foreign key reference to ResolutionMemberVote entity"),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    CreatedBy = table.Column<int>(type: "int", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true, defaultValueSql: "GETUTCDATE()"),
                    UpdatedBy = table.Column<int>(type: "int", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: true, defaultValue: false),
                    DeletedBy = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ResolutionMemberVoteStatusHistories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ResolutionMemberVoteStatusHistories_AspNetUsers_CreatedBy",
                        column: x => x.CreatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ResolutionMemberVoteStatusHistories_AspNetUsers_DeletedBy",
                        column: x => x.DeletedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ResolutionMemberVoteStatusHistories_AspNetUsers_UpdatedBy",
                        column: x => x.UpdatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ResolutionMemberVoteStatusHistories_Statuses",
                        column: x => x.StatusID,
                        principalTable: "ResolutionMemberVoteStatuses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ResolutionMemberVoteStatusHistories_Votes",
                        column: x => x.ResolutionMemberVoteID,
                        principalTable: "ResolutionMemberVotes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionItemVoteComments_BoardMemberId",
                table: "ResolutionItemVoteComments",
                column: "BoardMemberID");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionItemVoteComments_CreatedAt",
                table: "ResolutionItemVoteComments",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionItemVoteComments_CreatedBy",
                table: "ResolutionItemVoteComments",
                column: "CreatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionItemVoteComments_DeletedBy",
                table: "ResolutionItemVoteComments",
                column: "DeletedBy");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionItemVoteComments_ItemId_MemberId",
                table: "ResolutionItemVoteComments",
                columns: new[] { "ResolutionItemID", "BoardMemberID" });

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionItemVoteComments_ResolutionItemId",
                table: "ResolutionItemVoteComments",
                column: "ResolutionItemID");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionItemVoteComments_UpdatedBy",
                table: "ResolutionItemVoteComments",
                column: "UpdatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionItemVotes_CreatedBy",
                table: "ResolutionItemVotes",
                column: "CreatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionItemVotes_DeletedBy",
                table: "ResolutionItemVotes",
                column: "DeletedBy");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionItemVotes_Item_MemberVote_Unique",
                table: "ResolutionItemVotes",
                columns: new[] { "ResolutionItemId", "ResolutionMemberVoteId" },
                unique: true,
                filter: "[IsDeleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionItemVotes_ItemId",
                table: "ResolutionItemVotes",
                column: "ResolutionItemId");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionItemVotes_MemberVoteId",
                table: "ResolutionItemVotes",
                column: "ResolutionMemberVoteId");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionItemVotes_UpdatedBy",
                table: "ResolutionItemVotes",
                column: "UpdatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionItemVotes_VoteResult",
                table: "ResolutionItemVotes",
                column: "VoteResult");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionMemberVoteComments_CreatedAt",
                table: "ResolutionMemberVoteComments",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionMemberVoteComments_CreatedBy",
                table: "ResolutionMemberVoteComments",
                column: "CreatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionMemberVoteComments_DeletedBy",
                table: "ResolutionMemberVoteComments",
                column: "DeletedBy");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionMemberVoteComments_UpdatedBy",
                table: "ResolutionMemberVoteComments",
                column: "UpdatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionMemberVoteComments_VoteId",
                table: "ResolutionMemberVoteComments",
                column: "ResolutionMemberVoteID");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionMemberVotes_BoardMemberId",
                table: "ResolutionMemberVotes",
                column: "BoardMemberID");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionMemberVotes_CreatedBy",
                table: "ResolutionMemberVotes",
                column: "CreatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionMemberVotes_DeletedBy",
                table: "ResolutionMemberVotes",
                column: "DeletedBy");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionMemberVotes_Resolution_Member_Unique",
                table: "ResolutionMemberVotes",
                columns: new[] { "ResolutionId", "BoardMemberID" },
                unique: true,
                filter: "[IsDeleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionMemberVotes_ResolutionId",
                table: "ResolutionMemberVotes",
                column: "ResolutionId");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionMemberVotes_UpdatedBy",
                table: "ResolutionMemberVotes",
                column: "UpdatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionMemberVotes_VoteResult",
                table: "ResolutionMemberVotes",
                column: "VoteResult");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionMemberVoteStatuses_CreatedBy",
                table: "ResolutionMemberVoteStatuses",
                column: "CreatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionMemberVoteStatuses_DeletedBy",
                table: "ResolutionMemberVoteStatuses",
                column: "DeletedBy");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionMemberVoteStatuses_UpdatedBy",
                table: "ResolutionMemberVoteStatuses",
                column: "UpdatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionMemberVoteStatusHistories_CreatedAt",
                table: "ResolutionMemberVoteStatusHistories",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionMemberVoteStatusHistories_CreatedBy",
                table: "ResolutionMemberVoteStatusHistories",
                column: "CreatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionMemberVoteStatusHistories_DeletedBy",
                table: "ResolutionMemberVoteStatusHistories",
                column: "DeletedBy");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionMemberVoteStatusHistories_StatusId",
                table: "ResolutionMemberVoteStatusHistories",
                column: "StatusID");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionMemberVoteStatusHistories_UpdatedBy",
                table: "ResolutionMemberVoteStatusHistories",
                column: "UpdatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionMemberVoteStatusHistories_VoteId",
                table: "ResolutionMemberVoteStatusHistories",
                column: "ResolutionMemberVoteID");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ResolutionItemVoteComments");

            migrationBuilder.DropTable(
                name: "ResolutionItemVotes");

            migrationBuilder.DropTable(
                name: "ResolutionMemberVoteComments");

            migrationBuilder.DropTable(
                name: "ResolutionMemberVoteStatusHistories");

            migrationBuilder.DropTable(
                name: "ResolutionMemberVoteStatuses");

            migrationBuilder.DropTable(
                name: "ResolutionMemberVotes");

            migrationBuilder.CreateTable(
                name: "ResolutionVotes",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    BoardMemberId = table.Column<int>(type: "int", nullable: false),
                    CreatedBy = table.Column<int>(type: "int", nullable: false),
                    DeletedBy = table.Column<int>(type: "int", nullable: true),
                    ResolutionId = table.Column<int>(type: "int", nullable: false),
                    ResolutionItemId = table.Column<int>(type: "int", nullable: true),
                    UpdatedBy = table.Column<int>(type: "int", nullable: true),
                    Comments = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    VoteValue = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ResolutionVotes", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ResolutionVotes_AspNetUsers_CreatedBy",
                        column: x => x.CreatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ResolutionVotes_AspNetUsers_DeletedBy",
                        column: x => x.DeletedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ResolutionVotes_AspNetUsers_UpdatedBy",
                        column: x => x.UpdatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ResolutionVotes_BoardMembers_BoardMemberId",
                        column: x => x.BoardMemberId,
                        principalTable: "BoardMembers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ResolutionVotes_ResolutionItems_ResolutionItemId",
                        column: x => x.ResolutionItemId,
                        principalTable: "ResolutionItems",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ResolutionVotes_Resolutions_ResolutionId",
                        column: x => x.ResolutionId,
                        principalTable: "Resolutions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionVotes_BoardMemberId",
                table: "ResolutionVotes",
                column: "BoardMemberId");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionVotes_CreatedBy",
                table: "ResolutionVotes",
                column: "CreatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionVotes_DeletedBy",
                table: "ResolutionVotes",
                column: "DeletedBy");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionVotes_ResolutionId",
                table: "ResolutionVotes",
                column: "ResolutionId");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionVotes_ResolutionItemId",
                table: "ResolutionVotes",
                column: "ResolutionItemId");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionVotes_UpdatedBy",
                table: "ResolutionVotes",
                column: "UpdatedBy");
        }
    }
}
