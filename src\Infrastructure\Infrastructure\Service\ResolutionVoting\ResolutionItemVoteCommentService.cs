using Abstraction.Contract.Service.ResolutionVoting;
using Abstraction.Contracts.Repository;
using AutoMapper;
using Domain.Entities.ResolutionManagement;
using Infrastructure.Service;
using Microsoft.Extensions.Localization;
using Resources;

namespace Infrastructure.Onion.Service.ResolutionVoting
{
    /// <summary>
    /// Service implementation for ResolutionItemVoteComment operations
    /// Inherits from BaseService to provide standard CRUD operations
    /// Implements IResolutionItemVoteCommentService interface
    /// Follows the established service implementation pattern in the project
    /// </summary>
    public class ResolutionItemVoteCommentService : BaseService<ResolutionItemVoteComment>, IResolutionItemVoteCommentService
    {
        /// <summary>
        /// Initializes a new instance of the ResolutionItemVoteCommentService class
        /// </summary>
        /// <param name="repository">Generic repository for data access operations</param>
        /// <param name="mapper">AutoMapper instance for object mapping</param>
        /// <param name="localizer">String localizer for localization support</param>
        public ResolutionItemVoteCommentService(
            IGenericRepository repository,
            IMapper mapper,
            IStringLocalizer<SharedResources> localizer)
            : base(repository, mapper, localizer)
        {
        }

        // Additional ResolutionItemVoteComment-specific methods can be implemented here if needed
        // For example:
        // public async Task<BaseResponse<IEnumerable<ResolutionItemVoteCommentDto>>> GetCommentsByResolutionItemAsync(int resolutionItemId)
        // {
        //     // Implementation for getting comments by resolution item
        // }
        // public async Task<BaseResponse<IEnumerable<ResolutionItemVoteCommentDto>>> GetCommentsByBoardMemberAsync(int boardMemberId)
        // {
        //     // Implementation for getting comments by board member
        // }
    }
}
