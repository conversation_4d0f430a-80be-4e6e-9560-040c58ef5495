using Abstraction.Base.Response;
using Abstraction.Enums;
using Application.Base.Abstracts;
using Application.Features.Shared.FileManagment.Dtos;

namespace Application.Features.Shared.FileManagment.Commands.MinIOPreview
{
    /// <summary>
    /// Command for generating preview URLs for files in MinIO storage
    /// </summary>
    public record MinIOPreviewCommand : ICommand<BaseResponse<MinIOPreviewDto>>
    {
        /// <summary>
        /// Attachment ID to generate preview for
        /// </summary>
        public int? Id { get; set; }

        /// <summary>
        /// Optional bucket name override (if not provided, will determine from attachment module)
        /// </summary>
        public string? BucketName { get; set; }

        /// <summary>
        /// URL expiry time in minutes (default: 10080 minutes = 7 days for effectively no expiry)
        /// </summary>
        public int ExpiryInMinutes { get; set; } = 10080;
    }  
}
