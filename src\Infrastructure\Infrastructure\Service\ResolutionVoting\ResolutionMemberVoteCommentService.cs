using Abstraction.Contract.Service.ResolutionVoting;
using Abstraction.Contracts.Repository;
using AutoMapper;
using Domain.Entities.ResolutionManagement;
using Infrastructure.Service;
using Microsoft.Extensions.Localization;
using Resources;

namespace Infrastructure.Onion.Service.ResolutionVoting
{
    /// <summary>
    /// Service implementation for ResolutionMemberVoteComment operations
    /// Inherits from BaseService to provide standard CRUD operations
    /// Implements IResolutionMemberVoteCommentService interface
    /// Follows the established service implementation pattern in the project
    /// </summary>
    public class ResolutionMemberVoteCommentService : BaseService<ResolutionMemberVoteComment>, IResolutionMemberVoteCommentService
    {
        /// <summary>
        /// Initializes a new instance of the ResolutionMemberVoteCommentService class
        /// </summary>
        /// <param name="repository">Generic repository for data access operations</param>
        /// <param name="mapper">AutoMapper instance for object mapping</param>
        /// <param name="localizer">String localizer for localization support</param>
        public ResolutionMemberVoteCommentService(
            IGenericRepository repository,
            IMapper mapper,
            IStringLocalizer<SharedResources> localizer)
            : base(repository, mapper, localizer)
        {
        }

        // Additional ResolutionMemberVoteComment-specific methods can be implemented here if needed
        // For example:
        // public async Task<BaseResponse<IEnumerable<ResolutionMemberVoteCommentDto>>> GetCommentsByVoteAsync(int resolutionMemberVoteId)
        // {
        //     // Implementation for getting comments by vote
        // }
    }
}
