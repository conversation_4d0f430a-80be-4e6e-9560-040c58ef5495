﻿using Abstraction.Contract.Service.ResolutionVoting;
using Abstraction.Contract.Service.Storage;
using Abstraction.Contracts.Repository;
using Abstraction.Contracts.Service;
using Abstraction.Contracts.Service.Catalog;
using Application.Common.Configurations;
using AutoMapper;
using Infrastructure.Onion.Service.ResolutionVoting;
using Infrastructure.Service.Catalog;
using Infrastructure.Service.Storage;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Minio;
using NLog;
using Resources;

namespace Infrastructure.Service
{
    public class ServiceManager : IServiceManager
    {
        private readonly Lazy<IProductService> _productService;
        private readonly Lazy<IStorageService> _storageService;
        private readonly Lazy<IPreviewUrlHelper> _previewUrlHelper;

        // Resolution Voting Services
        private readonly Lazy<IResolutionMemberVoteService> _resolutionMemberVoteService;
        private readonly Lazy<IResolutionMemberVoteCommentService> _resolutionMemberVoteCommentService;
        private readonly Lazy<IResolutionItemVoteCommentService> _resolutionItemVoteCommentService;
        private readonly IGenericRepository _repository;
        private readonly IMapper _mapper;
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly IMinioClient _minioClient;
        private readonly ILogger<StorageService> _logger;
        private readonly ILogger<PreviewUrlHelper> _previewUrlLogger;
        private readonly IOptions<MinIOConfiguration> _minioConfig;

        public ServiceManager(IMinioClient minioClient, IOptions<MinIOConfiguration> config, ILogger<StorageService> logger, ILogger<PreviewUrlHelper> previewUrlLogger, IGenericRepository repository, IMapper mapper, IStringLocalizer<SharedResources> localizer)
        {
            _repository = repository;
            _mapper = mapper;
            _localizer = localizer;
            _minioClient = minioClient;
            _minioConfig = config;
            _logger = logger;
            _previewUrlLogger = previewUrlLogger;
            _productService = new Lazy<IProductService>(() => new ProductService(_repository, _mapper, _localizer));
            _storageService = new Lazy<IStorageService>(() => new StorageService(_minioClient, _minioConfig, _logger));
            _previewUrlHelper = new Lazy<IPreviewUrlHelper>(() => new PreviewUrlHelper(this, _minioConfig, _previewUrlLogger));

            // Initialize Resolution Voting Services
            _resolutionMemberVoteService = new Lazy<IResolutionMemberVoteService>(() => new ResolutionMemberVoteService(_repository, _mapper, _localizer));
            _resolutionMemberVoteCommentService = new Lazy<IResolutionMemberVoteCommentService>(() => new ResolutionMemberVoteCommentService(_repository, _mapper, _localizer));
            _resolutionItemVoteCommentService = new Lazy<IResolutionItemVoteCommentService>(() => new ResolutionItemVoteCommentService(_repository, _mapper, _localizer));


        }
        public IProductService ProductService => _productService.Value;
        public IStorageService StorageService => _storageService.Value;
        public IPreviewUrlHelper PreviewUrlHelper => _previewUrlHelper.Value;

        // Resolution Voting Service Properties
        public IResolutionMemberVoteService ResolutionMemberVoteService => _resolutionMemberVoteService.Value;
        public IResolutionMemberVoteCommentService ResolutionMemberVoteCommentService => _resolutionMemberVoteCommentService.Value;
        public IResolutionItemVoteCommentService ResolutionItemVoteCommentService => _resolutionItemVoteCommentService.Value;

    }
}
