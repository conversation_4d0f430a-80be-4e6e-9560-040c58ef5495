using Abstraction.Base.Dto;
using Abstraction.Base.Response;
using Abstraction.Common.Wappers;
using Abstraction.Contract.Service.ResolutionVoting;
using Domain.Entities.ResolutionManagement;
using Infrastructure.Dto.ResolutionVoting;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Presentation.Bases;

namespace Presentation.Controllers.ResolutionVoting
{
    /// <summary>
    /// Controller for ResolutionItemVoteComment entity operations
    /// Provides full CRUD operations following service-based architecture strategy pattern
    /// Handles comment operations for resolution item votes in the Jadwa Fund Management System
    /// </summary>
    [Route("api/ResolutionVoting/[controller]/[action]")]
    [ApiController]
    public class ResolutionItemVoteCommentController : BaseController
    {
        private readonly IResolutionItemVoteCommentService _resolutionItemVoteCommentService;

        public ResolutionItemVoteCommentController(IResolutionItemVoteCommentService resolutionItemVoteCommentService)
        {
            _resolutionItemVoteCommentService = resolutionItemVoteCommentService;
        }

        /// <summary>
        /// Create a new resolution item vote comment
        /// </summary>
        /// <param name="entity">Resolution item vote comment data</param>
        /// <returns>Success message or validation errors</returns>
        [HttpPost]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status422UnprocessableEntity)]
        public virtual async Task<IActionResult> CreateResolutionItemVoteComment([FromBody] ResolutionItemVoteCommentDto entity)
        {
            var returnValue = await _resolutionItemVoteCommentService.AddAsync(entity);
            return NewResult(returnValue);
        }

        /// <summary>
        /// Update an existing resolution item vote comment
        /// </summary>
        /// <param name="entity">Updated resolution item vote comment data</param>
        /// <returns>Success message or validation errors</returns>
        [HttpPut]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status422UnprocessableEntity)]
        public virtual async Task<IActionResult> UpdateResolutionItemVoteComment([FromBody] ResolutionItemVoteCommentDto entity)
        {
            var returnValue = await _resolutionItemVoteCommentService.UpdateAsync(entity);
            return NewResult(returnValue);
        }

        /// <summary>
        /// Get resolution item vote comment by ID
        /// </summary>
        /// <param name="id">Resolution item vote comment ID</param>
        /// <returns>Resolution item vote comment details</returns>
        [HttpGet]
        [ProducesResponseType(typeof(BaseResponse<ResolutionItemVoteCommentDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetResolutionItemVoteCommentById(int id)
        {
            var returnValue = await _resolutionItemVoteCommentService.GetByIdAsync<ResolutionItemVoteCommentDto>(id, false);
            return NewResult(returnValue);
        }

        /// <summary>
        /// Get paginated list of resolution item vote comments
        /// </summary>
        /// <param name="query">Pagination and filtering parameters</param>
        /// <returns>Paginated list of resolution item vote comments</returns>
        [HttpGet]
        [ProducesResponseType(typeof(PaginatedResult<ResolutionItemVoteCommentDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ResolutionItemVoteCommentList([FromQuery] BaseListDto query)
        {
            var returnValue = await _resolutionItemVoteCommentService.GetAllPagedAsync<ResolutionItemVoteCommentDto>(query.PageNumber, query.PageSize, query.OrderBy, false);
            return NewResult(returnValue);
        }

        /// <summary>
        /// Get comments by resolution item ID
        /// </summary>
        /// <param name="resolutionItemId">Resolution item ID</param>
        /// <param name="query">Pagination parameters</param>
        /// <returns>Paginated list of comments for the specified resolution item</returns>
        [HttpGet]
        [ProducesResponseType(typeof(PaginatedResult<ResolutionItemVoteCommentDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetCommentsByResolutionItem(int resolutionItemId, [FromQuery] BaseListDto query)
        {
            // Note: This would require a custom service method to filter by resolution item ID
            // For now, using the standard GetAllPagedAsync method
            var returnValue = await _resolutionItemVoteCommentService.GetAllPagedAsync<ResolutionItemVoteCommentDto>(query.PageNumber, query.PageSize, query.OrderBy, false);
            return NewResult(returnValue);
        }

        /// <summary>
        /// Get comments by board member ID
        /// </summary>
        /// <param name="boardMemberId">Board member ID</param>
        /// <param name="query">Pagination parameters</param>
        /// <returns>Paginated list of comments made by the specified board member</returns>
        [HttpGet]
        [ProducesResponseType(typeof(PaginatedResult<ResolutionItemVoteCommentDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetCommentsByBoardMember(int boardMemberId, [FromQuery] BaseListDto query)
        {
            // Note: This would require a custom service method to filter by board member ID
            // For now, using the standard GetAllPagedAsync method
            var returnValue = await _resolutionItemVoteCommentService.GetAllPagedAsync<ResolutionItemVoteCommentDto>(query.PageNumber, query.PageSize, query.OrderBy, false);
            return NewResult(returnValue);
        }

        /// <summary>
        /// Delete a resolution item vote comment
        /// </summary>
        /// <param name="id">Resolution item vote comment ID</param>
        /// <returns>Success message or error</returns>
        [HttpDelete]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteResolutionItemVoteComment(int id)
        {
            var returnValue = await _resolutionItemVoteCommentService.DeleteAsync(id);
            return NewResult(returnValue);
        }
    }
}
