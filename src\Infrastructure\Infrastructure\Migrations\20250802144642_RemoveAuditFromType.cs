﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class RemoveAuditFromType : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ResolutionTypes_AspNetUsers_CreatedBy",
                table: "ResolutionTypes");

            migrationBuilder.DropForeignKey(
                name: "FK_ResolutionTypes_AspNetUsers_DeletedBy",
                table: "ResolutionTypes");

            migrationBuilder.DropForeignKey(
                name: "FK_ResolutionTypes_AspNetUsers_UpdatedBy",
                table: "ResolutionTypes");

            migrationBuilder.DropIndex(
                name: "IX_ResolutionTypes_CreatedBy",
                table: "ResolutionTypes");

            migrationBuilder.DropIndex(
                name: "IX_ResolutionTypes_DeletedBy",
                table: "ResolutionTypes");

            migrationBuilder.DropIndex(
                name: "IX_ResolutionTypes_UpdatedBy",
                table: "ResolutionTypes");

            migrationBuilder.DropColumn(
                name: "CreatedAt",
                table: "ResolutionTypes");

            migrationBuilder.DropColumn(
                name: "CreatedBy",
                table: "ResolutionTypes");

            migrationBuilder.DropColumn(
                name: "DeletedAt",
                table: "ResolutionTypes");

            migrationBuilder.DropColumn(
                name: "DeletedBy",
                table: "ResolutionTypes");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "ResolutionTypes");

            migrationBuilder.DropColumn(
                name: "UpdatedAt",
                table: "ResolutionTypes");

            migrationBuilder.DropColumn(
                name: "UpdatedBy",
                table: "ResolutionTypes");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedAt",
                table: "ResolutionTypes",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<int>(
                name: "CreatedBy",
                table: "ResolutionTypes",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletedAt",
                table: "ResolutionTypes",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "DeletedBy",
                table: "ResolutionTypes",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "ResolutionTypes",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "UpdatedAt",
                table: "ResolutionTypes",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "UpdatedBy",
                table: "ResolutionTypes",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionTypes_CreatedBy",
                table: "ResolutionTypes",
                column: "CreatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionTypes_DeletedBy",
                table: "ResolutionTypes",
                column: "DeletedBy");

            migrationBuilder.CreateIndex(
                name: "IX_ResolutionTypes_UpdatedBy",
                table: "ResolutionTypes",
                column: "UpdatedBy");

            migrationBuilder.AddForeignKey(
                name: "FK_ResolutionTypes_AspNetUsers_CreatedBy",
                table: "ResolutionTypes",
                column: "CreatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_ResolutionTypes_AspNetUsers_DeletedBy",
                table: "ResolutionTypes",
                column: "DeletedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_ResolutionTypes_AspNetUsers_UpdatedBy",
                table: "ResolutionTypes",
                column: "UpdatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
