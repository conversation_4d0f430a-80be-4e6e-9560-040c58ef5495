﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Controllers\Cataloge\CategoryController.cs" />
    <Compile Remove="Controllers\Cataloge\DemoEntityController.cs" />
    <Compile Remove="Controllers\Cataloge\ProductController.cs" />
    <Compile Remove="Controllers\ResolutionVoting\ResolutionItemVoteCommentController.cs" />
    <Compile Remove="Controllers\ResolutionVoting\ResolutionMemberVoteCommentController.cs" />
  </ItemGroup>
	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.JsonPatch" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Core" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" />
	</ItemGroup>
	<ItemGroup>
	  <ProjectReference Include="..\..\Core\Application\Application.csproj" />
	  <ProjectReference Include="..\Infrastructure\Infrastructure.csproj" />

	</ItemGroup>
	<ItemGroup>
	  <Folder Include="Controllers\Cataloge\" />
	</ItemGroup>
</Project>
