using Abstraction.Contracts.Service;
using Domain.Entities.ResolutionManagement;

namespace Abstraction.Contract.Service.ResolutionVoting
{
    /// <summary>
    /// Service interface for ResolutionMemberVote operations
    /// Inherits from IBaseService to provide standard CRUD operations
    /// Follows the established service interface pattern in the project
    /// </summary>
    public interface IResolutionMemberVoteService : IBaseService<ResolutionMemberVote>
    {
        // Additional ResolutionMemberVote-specific methods can be added here if needed
        // For example:
        // Task<BaseResponse<bool>> HasMemberVotedAsync(int resolutionId, int boardMemberId);
        // Task<BaseResponse<IEnumerable<ResolutionMemberVoteDto>>> GetVotesByResolutionAsync(int resolutionId);
    }
}
