using Abstraction.Base.Response;
using Abstraction.Common.Wappers;
using Abstraction.Contract.Service;
using Abstraction.Contract.Service.Storage;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Abstraction.Contracts.Service;
using Abstraction.Enums;
using Application.Base.Abstracts;
using Application.Common.Helpers;
using Application.Features.DocumentManagement.Dtos;
using Application.Features.Resolutions.Dtos;
using AutoMapper;
using Domain.Entities.DocumentManagement;
using Microsoft.Extensions.Localization;
using Resources;
using System.Globalization;


namespace Application.Features.DocumentManagement.Queries.List
{
    /// <summary>
    /// Handler for GetDocuments operation
    /// Implements business logic for retrieving documents with filtering
    /// Follows Clean Architecture and CQRS patterns
    /// </summary>
    public class ListQueryHandler : BaseResponseHandler, IQueryHandler<ListQuery, PaginatedResult<DocumentDto>>
    {
        #region Fields

        private readonly IRepositoryManager _repository;
        private readonly ICurrentUserService _currentUserService;
        private readonly IMapper _mapper;
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly ILoggerManager _logger;
        private readonly IServiceManager _serviceManager;

        #endregion

        #region Constructor

        public ListQueryHandler(
            IRepositoryManager repository,
            ICurrentUserService currentUserService,
            IMapper mapper,
            IStringLocalizer<SharedResources> localizer,
            ILoggerManager logger,
            IServiceManager serviceManager)
        {
            _repository = repository;
            _currentUserService = currentUserService;
            _mapper = mapper;
            _localizer = localizer;
            _logger = logger;
            _serviceManager = serviceManager;

        }

        #endregion

        #region Handler Implementation

        public async Task<PaginatedResult<DocumentDto>> Handle(ListQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInfo($"Starting GetDocuments operation for user: {_currentUserService.UserId}");
                if (request == null)
                    return PaginatedResult<DocumentDto>.ServerError(_localizer[SharedResourcesKey.AnErrorIsOccurredWhileSavingData]);

                // Get documents based on filters
                var result = _repository.DocumentRepository.GetDocumentsWithFiltersAsync(
                    categoryId: request.CategoryId,
                    searchTerm: request.Search,
                    fundId:request.FundId
                    );
                if (!result.Any())
                {
                    return PaginatedResult<DocumentDto>.EmptyCollection(_localizer[SharedResourcesKey.NoRecords]);
                }
                var documentList = await _mapper.ProjectTo<DocumentDto>(result).ToPaginatedListAsync(request.PageNumber, request.PageSize,"");
                foreach (var document in documentList.Data)
                {
                    // Populate attachment preview URL
                   
                        document.PreviewUrl = await _serviceManager.PreviewUrlHelper.GeneratePreviewUrlAsync(
                            document.PreviewUrl,
                            (int)ModuleEnum.Documents,
                            cancellationToken);
                    document.DownloadUrl = document.PreviewUrl;
                }
                return documentList;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error occurred while retrieving documents for user: {_currentUserService.UserId}");
                return PaginatedResult<DocumentDto>.ServerError(_localizer[SharedResourcesKey.SystemErrorSavingData]);
            }
        }

        #endregion
    }
}
