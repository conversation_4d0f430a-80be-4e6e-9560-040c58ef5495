using Domain.Entities.FundManagement;
using Domain.Entities.ResolutionManagement;
using Domain.Entities.Shared;

namespace Domain.Services
{
    /// <summary>
    /// Domain service for voting business rules and operations
    /// Encapsulates complex business logic related to resolution voting
    /// Based on voting requirements for resolution management system
    /// </summary>
    public class VotingDomainService
    {
        

        ///// <summary>
        ///// Calculates voting results for a resolution based on voting methodology
        ///// </summary>
        ///// <param name="resolution">Resolution to calculate results for</param>
        ///// <param name="votes">All active votes for the resolution</param>
        ///// <param name="eligibleMembers">All eligible board members for voting</param>
        ///// <returns>Voting result with approval status and details</returns>
        //public static VotingResult CalculateResolutionResult(
        //    Resolution resolution,
        //    IEnumerable<ResolutionMemberVote> votes,
        //    IEnumerable<BoardMember> eligibleMembers)
        //{
        //    var result = new VotingResult();
            
        //    // Get resolution-level votes (using new voting system)
        //    var resolutionVotes = votes.Where(v => !v.IsDeleted.HasValue || !v.IsDeleted.Value).ToList();
            
        //    // If resolution has items, calculate based on item voting
        //    if (resolution.ResolutionItems.Any())
        //    {
        //        return CalculateItemBasedResult(resolution, votes, eligibleMembers);
        //    }

        //    // Calculate based on resolution-level voting
        //    return CalculateDirectResolutionResult(resolution, resolutionVotes, eligibleMembers);
        //}

        ///// <summary>
        ///// Calculates voting results based on resolution items
        ///// </summary>
        //private static VotingResult CalculateItemBasedResult(
        //    Resolution resolution,
        //    IEnumerable<ResolutionMemberVote> votes,
        //    IEnumerable<BoardMember> eligibleMembers)
        //{
        //    var result = new VotingResult();
        //    var itemResults = new List<ItemVotingResult>();

        //    // Calculate results for each resolution item using the new ResolutionItemVote system
        //    foreach (var item in resolution.ResolutionItems)
        //    {
        //        var itemResult = CalculateItemResultFromItemVotes(item, votes, eligibleMembers.ToList(), resolution.VotingType);
        //        itemResults.Add(itemResult);
        //    }

        //    // Apply member voting result logic (AllItems vs MajorityOfItems)
        //    if (resolution.MemberVotingResult == MemberVotingResult.AllItems)
        //    {
        //        result.IsApproved = itemResults.All(r => r.IsApproved);
        //    }
        //    else // MajorityOfItems
        //    {
        //        var approvedCount = itemResults.Count(r => r.IsApproved);
        //        result.IsApproved = approvedCount > (itemResults.Count / 2.0);
        //    }

        //    // Set overall statistics
        //    result.ItemResults = itemResults;
        //    result.TotalEligibleVoters = eligibleMembers.Count();
        //    result.TotalVotesCast = votes.Count(v => v.VoteResult != VoteResult.NotVotedYet);
        //    result.ApproveVotes = itemResults.Sum(r => r.ApproveVotes);
        //    result.RejectVotes = itemResults.Sum(r => r.RejectVotes);

        //    return result;
        //}

        ///// <summary>
        ///// Calculates voting results for direct resolution voting (no items)
        ///// </summary>
        //private static VotingResult CalculateDirectResolutionResult(
        //    Resolution resolution,
        //    IList<ResolutionMemberVote> resolutionVotes,
        //    IEnumerable<BoardMember> eligibleMembers)
        //{
        //    var result = new VotingResult();
        //    var eligibleCount = eligibleMembers.Count();
        //    var approveVotes = resolutionVotes.Count(v => v.VoteResult == VoteResult.Accept);
        //    var rejectVotes = resolutionVotes.Count(v => v.VoteResult == VoteResult.Reject);

        //    if (resolution.VotingType == VotingType.AllMembers)
        //    {
        //        // Unanimous approval required
        //        result.IsApproved = approveVotes == eligibleCount && rejectVotes == 0;
        //    }
        //    else // Majority
        //    {
        //        // Majority approval required
        //        result.IsApproved = approveVotes > (eligibleCount / 2.0);
        //    }

        //    result.TotalEligibleVoters = eligibleCount;
        //    result.TotalVotesCast = resolutionVotes.Count;
        //    result.ApproveVotes = approveVotes;
        //    result.RejectVotes = rejectVotes;
        //    result.AbstainVotes = 0; // New voting system doesn't have abstain option

        //    return result;
        //}

 
        ///// <summary>
        ///// Calculates voting result for a single resolution item using ResolutionItemVote entities
        ///// </summary>
        //private static ItemVotingResult CalculateItemResultFromItemVotes(
        //    ResolutionItem item,
        //    IEnumerable<ResolutionMemberVote> memberVotes,
        //    IList<BoardMember> eligibleMembers,
        //    VotingType votingMethodology)
        //{
        //    var result = new ItemVotingResult
        //    {
        //        ItemId = item.Id,
        //        ItemTitle = item.Title
        //    };

        //    // Get all ResolutionItemVote entities for this specific item
        //    var itemVotes = memberVotes
        //        .SelectMany(mv => mv.ResolutionItemVotes ?? new List<ResolutionItemVote>())
        //        .Where(iv => iv.ResolutionItemId == item.Id && (!iv.IsDeleted.HasValue || !iv.IsDeleted.Value))
        //        .ToList();

        //    var eligibleCount = eligibleMembers.Count;
        //    var approveVotes = itemVotes.Count(v => v.VoteResult == VoteResult.Accept);
        //    var rejectVotes = itemVotes.Count(v => v.VoteResult == VoteResult.Reject);
        //    var notEligibleCount = itemVotes.Count(v => v.VoteResult == VoteResult.NotEligibleToVote);

        //    // Adjust eligible count for members who are not eligible to vote on this item
        //    var actualEligibleCount = eligibleCount - notEligibleCount;

        //    if (votingMethodology == VotingType.AllMembers)
        //    {
        //        result.IsApproved = actualEligibleCount > 0 && approveVotes == actualEligibleCount && rejectVotes == 0;
        //    }
        //    else // Majority
        //    {
        //        result.IsApproved = actualEligibleCount > 0 && approveVotes > (actualEligibleCount / 2.0);
        //    }

        //    result.EligibleVoters = actualEligibleCount;
        //    result.VotesCast = approveVotes + rejectVotes;
        //    result.ApproveVotes = approveVotes;
        //    result.RejectVotes = rejectVotes;

        //    return result;
        //}

        public static ItemVotingResult CalculateItemResult(ResolutionItem item, List<ResolutionItemVote> itemVotes)
        {
            var result = new ItemVotingResult();
            result.EligibleVoters = itemVotes.Count() - itemVotes.Where(c => c.VoteResult == VoteResult.NotEligibleToVote).Count();
            var approveVotes = itemVotes.Count(v => v.VoteResult == VoteResult.Accept);
            var rejectVotes = itemVotes.Count(v => v.VoteResult == VoteResult.Reject);
            var notVotedYetCount = itemVotes.Count(v => v.VoteResult == VoteResult.NotVotedYet);
            result.VotesCast = itemVotes.Where(c => c.VoteResult == VoteResult.Accept || c.VoteResult == VoteResult.Reject).Count();
            result.ApproveVotes = approveVotes;
            result.PendingVotes = notVotedYetCount;
            result.RejectVotes = rejectVotes;
            return result;
        }

        public static VoteResult CalculateResolutionMemberVote(List<ResolutionItemVote> memberVotes ,MemberVotingResult resolutionMemberVotingResult)
        {
            var allElgigibleVoteCount = memberVotes.Where(c => c.VoteResult != VoteResult.NotEligibleToVote).Count();
            var notVotedYet = memberVotes.Where(c => c.VoteResult == VoteResult.NotVotedYet).Any();
            if (notVotedYet)
            {
                return VoteResult.NotVotedYet;
            }
            else if (resolutionMemberVotingResult == MemberVotingResult.AllItems && !memberVotes.All(c => c.VoteResult == VoteResult.Accept))
            {
                return VoteResult.Reject;
            }
            else if (resolutionMemberVotingResult == MemberVotingResult.MajorityOfItems &&  memberVotes.Where(c=> c.VoteResult == VoteResult.Accept).Count() < (allElgigibleVoteCount/2.0))
            {
                return VoteResult.Reject;
            }
            else
            {
                return VoteResult.Accept;
            }
        }

        ///// <summary>
        ///// Suspends all active votes for a resolution (used when resolution is edited during voting)
        ///// </summary>
        ///// <param name="votes">All votes for the resolution</param>
        ///// <returns>Number of votes suspended</returns>
        //public static int SuspendActiveVotes(IEnumerable<ResolutionMemberVote> votes)
        //{
        //    var activeVotes = votes.Where(v => !v.IsDeleted.HasValue || !v.IsDeleted.Value).ToList();
        //    foreach (var vote in activeVotes)
        //    {
        //        vote.IsDeleted = true;
        //        vote.DeletedAt = DateTime.UtcNow;
        //    }
        //    return activeVotes.Count;
        //}

        ///// <summary>
        ///// Calculates the resolution vote result based on item votes
        ///// </summary>
        ///// <param name="itemVotes">List of item votes for the resolution</param>
        ///// <param name="memberVotingResult">Member voting result methodology</param>
        ///// <returns>Calculated resolution vote result</returns>
        //public static VoteResult CalculateResolutionVoteFromItemVotes(IEnumerable<ResolutionItemVote> itemVotes,MemberVotingResult memberVotingResult)
        //{
        //    var itemVotesList = itemVotes.Where(iv => iv.VoteResult != VoteResult.NotVotedYet && iv.VoteResult != VoteResult.NotEligibleToVote).ToList();

        //    if (!itemVotesList.Any())
        //    {
        //        return VoteResult.NotVotedYet;
        //    }

        //    var acceptedItems = itemVotesList.Count(iv => iv.VoteResult == VoteResult.Accept);
        //    var totalVotedItems = itemVotesList.Count;

        //    if (memberVotingResult == MemberVotingResult.AllItems)
        //    {
        //        // All items must be accepted
        //        return acceptedItems == totalVotedItems ? VoteResult.Accept : VoteResult.Reject;
        //    }
        //    else // MajorityOfItems
        //    {
        //        // Majority of items must be accepted
        //        return acceptedItems > (totalVotedItems / 2.0) ? VoteResult.Accept : VoteResult.Reject;
        //    }
        //}

        /// <summary>
        /// Creates initial voting records when resolution is sent to vote
        /// </summary>
        /// <param name="resolution">Resolution being sent to vote</param>
        /// <param name="eligibleMembers">List of eligible board members</param>
        /// <param name="conflicts">List of resolution item conflicts</param>
        /// <returns>List of created ResolutionMemberVote records</returns>
        public static List<ResolutionMemberVote> CreateInitialVotingRecords(Resolution resolution,IEnumerable<BoardMember> eligibleMembers,IEnumerable<ResolutionItemConflict> conflicts)
        {
            var memberVotes = new List<ResolutionMemberVote>();
            var conflictsByItem = conflicts.GroupBy(c => c.ResolutionItemId).ToDictionary(g => g.Key, g => g.ToList());

            foreach (var member in eligibleMembers)
            {
                var memberVote = new ResolutionMemberVote
                {
                    ResolutionId = resolution.Id,
                    BoardMemberID = member.Id,
                    VoteResult = VoteResult.NotVotedYet,
                    ResolutionItemVotes = new List<ResolutionItemVote>()
                };
                if(resolution.ResolutionItems.Any())
                {
                    // Create ResolutionItemVote for each resolution item
                    foreach (var item in resolution.ResolutionItems)
                    {
                        var hasConflict = conflictsByItem.ContainsKey(item.Id) && conflictsByItem[item.Id].Any(c => c.BoardMemberId == member.Id);

                        var itemVote = new ResolutionItemVote
                        {
                            ResolutionItemId = item.Id,
                            VoteResult = hasConflict ? VoteResult.NotEligibleToVote : VoteResult.NotVotedYet
                        };

                        memberVote.ResolutionItemVotes.Add(itemVote);
                    }
                }             
                memberVotes.Add(memberVote);
            }
            return memberVotes;
        }
    }

    /// <summary>
    /// Represents the result of voting calculation for a resolution
    /// </summary>
    public class VotingResult
    {
        public bool IsApproved { get; set; }
        public int TotalEligibleVoters { get; set; }
        public int TotalVotesCast { get; set; }
        public int ApproveVotes { get; set; }
        public int RejectVotes { get; set; }
        public int AbstainVotes { get; set; }
        public IEnumerable<ItemVotingResult> ItemResults { get; set; } = new List<ItemVotingResult>();
    }

    /// <summary>
    /// Represents the voting result for a single resolution item
    /// </summary>
    public class ItemVotingResult
    {
         
       
        public int EligibleVoters { get; set; }
        public int VotesCast { get; set; }
        public int ApproveVotes { get; set; }
        public int RejectVotes { get; set; }
        public int PendingVotes { get; set; }
        public ItemVoteResult VoteResult
        {
            get
            {
                if (VotesCast < EligibleVoters)
                    return ItemVoteResult.VotingInProgress;
                else if (RejectVotes > ApproveVotes && VotesCast == EligibleVoters)
                    return ItemVoteResult.Rejected;
                else if (ApproveVotes > RejectVotes && VotesCast == EligibleVoters)
                    return ItemVoteResult.Accepted;
                else if (ApproveVotes == RejectVotes && VotesCast == EligibleVoters)
                    return ItemVoteResult.Accepted;
                else
                    return ItemVoteResult.VotingInProgress;
            }
        }
       
        
    }
}
