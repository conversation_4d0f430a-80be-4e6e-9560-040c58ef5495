using Abstraction.Base.Dto;
using Abstraction.Base.Response;
using Abstraction.Common.Wappers;
using Abstraction.Contract.Service.ResolutionVoting;
using Domain.Entities.ResolutionManagement;
using Infrastructure.Dto.ResolutionVoting;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Presentation.Bases;

namespace Presentation.Controllers.ResolutionVoting
{
    /// <summary>
    /// Controller for ResolutionMemberVoteComment entity operations
    /// Provides full CRUD operations following service-based architecture strategy pattern
    /// Handles comment operations for resolution member votes in the Jadwa Fund Management System
    /// </summary>
    [Route("api/ResolutionVoting/[action]")]
    [ApiController]
    public class ResolutionMemberVoteCommentController : BaseController
    {
        private readonly IResolutionMemberVoteCommentService _resolutionMemberVoteCommentService;

        public ResolutionMemberVoteCommentController(IResolutionMemberVoteCommentService resolutionMemberVoteCommentService)
        {
            _resolutionMemberVoteCommentService = resolutionMemberVoteCommentService;
        }

        /// <summary>
        /// Create a new resolution member vote comment
        /// </summary>
        /// <param name="entity">Resolution member vote comment data</param>
        /// <returns>Success message or validation errors</returns>
        [HttpPost]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status422UnprocessableEntity)]
        public virtual async Task<IActionResult> CreateResolutionMemberVoteComment([FromBody] ResolutionMemberVoteCommentDto entity)
        {
            var returnValue = await _resolutionMemberVoteCommentService.AddAsync(entity);
            return NewResult(returnValue);
        }

        /// <summary>
        /// Update an existing resolution member vote comment
        /// </summary>
        /// <param name="entity">Updated resolution member vote comment data</param>
        /// <returns>Success message or validation errors</returns>
        [HttpPut]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status422UnprocessableEntity)]
        public virtual async Task<IActionResult> UpdateResolutionMemberVoteComment([FromBody] ResolutionMemberVoteCommentDto entity)
        {
            var returnValue = await _resolutionMemberVoteCommentService.UpdateAsync(entity);
            return NewResult(returnValue);
        }

        /// <summary>
        /// Get resolution member vote comment by ID
        /// </summary>
        /// <param name="id">Resolution member vote comment ID</param>
        /// <returns>Resolution member vote comment details</returns>
        [HttpGet]
        [ProducesResponseType(typeof(BaseResponse<ResolutionMemberVoteCommentDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetResolutionMemberVoteCommentById(int id)
        {
            var returnValue = await _resolutionMemberVoteCommentService.GetByIdAsync<ResolutionMemberVoteCommentDto>(id, false);
            return NewResult(returnValue);
        }

        /// <summary>
        /// Get paginated list of resolution member vote comments
        /// </summary>
        /// <param name="query">Pagination and filtering parameters</param>
        /// <returns>Paginated list of resolution member vote comments</returns>
        [HttpGet]
        [ProducesResponseType(typeof(PaginatedResult<ResolutionMemberVoteCommentDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ResolutionMemberVoteCommentList([FromQuery] BaseListDto query)
        {
            var returnValue = await _resolutionMemberVoteCommentService.GetAllPagedAsync<ResolutionMemberVoteCommentDto>(query.PageNumber, query.PageSize, query.OrderBy, false);
            return NewResult(returnValue);
        }

        /// <summary>
        /// Get comments by resolution member vote ID
        /// </summary>
        /// <param name="resolutionMemberVoteId">Resolution member vote ID</param>
        /// <param name="query">Pagination parameters</param>
        /// <returns>Paginated list of comments for the specified resolution member vote</returns>
        [HttpGet]
        [ProducesResponseType(typeof(PaginatedResult<ResolutionMemberVoteCommentDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetCommentsByResolutionMemberVote(int resolutionMemberVoteId, [FromQuery] BaseListDto query)
        {
            // Note: This would require a custom service method to filter by resolution member vote ID
            // For now, using the standard GetAllPagedAsync method
            var returnValue = await _resolutionMemberVoteCommentService.GetAllPagedAsync<ResolutionMemberVoteCommentDto>(query.PageNumber, query.PageSize, query.OrderBy, false);
            return NewResult(returnValue);
        }

        /// <summary>
        /// Delete a resolution member vote comment
        /// </summary>
        /// <param name="id">Resolution member vote comment ID</param>
        /// <returns>Success message or error</returns>
        [HttpDelete]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteResolutionMemberVoteComment(int id)
        {
            var returnValue = await _resolutionMemberVoteCommentService.DeleteAsync(id);
            return NewResult(returnValue);
        }
    }
}
