using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class Add_PersonalPhotoFileId_To_User : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "PersonalPhotoFileId",
                table: "AspNetUsers",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUsers_PersonalPhotoFileId",
                table: "AspNetUsers",
                column: "PersonalPhotoFileId");

            migrationBuilder.AddForeignKey(
                name: "FK_AspNetUsers_Attachments_PersonalPhotoFileId",
                table: "AspNetUsers",
                column: "PersonalPhotoFileId",
                principalTable: "Attachments",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AspNetUsers_Attachments_PersonalPhotoFileId",
                table: "AspNetUsers");

            migrationBuilder.DropIndex(
                name: "IX_AspNetUsers_PersonalPhotoFileId",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "PersonalPhotoFileId",
                table: "AspNetUsers");
        }
    }
}
