

using System.ComponentModel.DataAnnotations.Schema;
using Abstraction.Base.Dto;
using Domain.Entities.ResolutionManagement;
using Domain.Entities.Users;

namespace Application.Features.ResolutionMemberVotes.Dtos
{
    public record ResolutionMemberVoteDto : BaseDto
    {
        /// <summary>
        /// Resolution identifier that this vote is for
        /// Foreign key reference to Resolution  entity
        /// </summary>
        public int ResolutionId { get; set; }

        public int CreatedBy { get; set; } // ID of the user who created this vote
        public DateTime CreatedAt { get; set; } // Timestamp of when the vote was created
        public List<ResolutionItemVoteDto>? ItemsVote { get; set; }
        public List<ResolutionVoteCommentDto>? VoteComments { get; set; }
       
    }
    public record ResolutionItemVoteDto : BaseDto
    {
        /// <summary>
        /// Resolution item identifier that this vote is for
        /// Foreign key reference to ResolutionItem entity
        /// </summary>
        public int ResolutionItemId { get; set; }
        /// <summary>
        /// Auto-generated title (Item1, Item2, etc.)
        /// </summary>
        public string Title { get; set; }  

        /// <summary>
        /// Description of the resolution item (max 500 characters)
        /// </summary>
        public string? Description { get; set; }  

        /// <summary>
        /// Display order for sorting resolution items
        /// </summary>
        public int DisplayOrder { get; set; }
        public List<ResolutionItemVoteCommentDto>? ItemComments { get; set; }

        /// <summary>
        /// The vote result for this specific resolution item
        /// Uses VoteResult enum (NotEligibleToVote = 0 , NotVotedYet = 1, Accept = 2, Reject = 3)
        /// </summary>
        public VoteResult VoteResult { get; set; }
        public string VoteResultDisplay { get; set; }
        public int CreatedBy { get; set; } // ID of the user who created this vote
        public DateTime CreatedAt { get; set; } // Timestamp of when the vote was created

    }
    public record ResolutionItemVoteCommentDto : BaseDto
    {
        /// <summary>
        /// Comment text providing additional information about the item vote
        /// Can be used to explain the reasoning behind the vote decision for this specific item
        /// </summary>
        public string? Comment { get; set; }
        public int CreatedBy { get; set; } // ID of the user who created this vote
        public DateTime CreatedAt { get; set; } // Timestamp of when the vote was created
        public string? CreatedByName { get; set; }
    }
    public record ResolutionVoteCommentDto : BaseDto
    {
        /// <summary>
        /// Comment text providing additional information about the item vote
        /// Can be used to explain the reasoning behind the vote decision for this specific item
        /// </summary>
        public string? Comment { get; set; }

        public int CreatedBy { get; set; } // ID of the user who created this vote
        public DateTime CreatedAt { get; set; } // Timestamp of when the vote was created
        public string? CreatedByName { get; set; }
    }
}
