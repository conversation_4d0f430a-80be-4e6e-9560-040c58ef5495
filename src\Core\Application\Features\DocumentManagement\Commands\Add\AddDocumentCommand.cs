using Abstraction.Base.Response;
using Application.Base.Abstracts;
using Application.Features.DocumentManagement.Dtos;
using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;

namespace Application.Features.DocumentManagement.Commands.Add
{
    /// <summary>
    /// Command for adding a document
    /// </summary>
    public class AddDocumentCommand : ICommand<BaseResponse<string>>
    {
        /// <summary>
        /// Document category ID
        /// </summary>
        public int DocumentCategoryId { get; set; }

        /// <summary>
        /// Attachment ID
        /// </summary>
        public int AttachmentId { get; set; }

        /// <summary>
        /// Fund ID
        /// </summary>
        public int FundId { get; set; }
    }
}
