using Abstraction.Base.Dto;
using Abstraction.Constants;
using Domain.Entities.ResolutionManagement;


namespace Application.Features.Resolutions.Dtos
{
    /// <summary>
    /// Data Transfer Object for ResolutionStatusHistory entity
    /// Contains status change history for resolutions
    /// Used for audit trail and status tracking
    /// </summary>
    public record ResolutionStatusHistoryDto : BaseDto
    {
        /// <summary>
        /// Resolution identifier
        /// </summary>
        public int ResolutionId { get; set; }

        /// <summary>
        /// Rejection reason when status is changed to "Rejected"
        /// Required when fund manager rejects a resolution
        /// </summary>
        public string? RejectionReason { get; set; } = string.Empty;

        /// <summary>
        /// User who made the status change
        /// </summary>
        public int CreatedBy { get; set; }

        /// <summary>
        /// Date and time of the status change
        /// </summary>
        public DateTime CreatedAt { get; set; }


        /// <summary>
        /// New status of the resolution after this action
        /// Used to track status transitions
        /// </summary>
        public ResolutionStatusEnum Status { get; set; }
        /// <summary>
        /// Resolution status information with localization
        /// </summary>
        public string DisplayedStatus { get; set; }

        /// <summary>
        /// Resolution status information with localization
        /// </summary>
        public string DisplayedAction { get; set; }

        /// <summary>
        /// Resolution status information with localization
        /// </summary>
        public ResolutionActionEnum Action { get; set; }

        /// <summary>
        /// Resolution status information with localization
        /// </summary>
        public string UserRole { get; set; }
        /// <summary>
        /// Resolution status information with localization
        /// </summary>
        public string DisplayedUserRole { get; set; }
         

        /// <summary>
        /// User full name who made the change
        /// </summary>
        public string FullName { get; set; } = string.Empty;



      
    }
}
