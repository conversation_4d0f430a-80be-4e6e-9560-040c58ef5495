﻿using AutoMapper;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Application.Features.ResolutionMemberVotes.Dtos;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Abstraction.Contract.Service;


namespace Application.Features.ResolutionMemberVotes.Queries.Get
{
    public class GetQueryHandler : BaseResponse<PERSON><PERSON>ler, IQueryHandler<GetQuery, BaseResponse<ResolutionMemberVoteDto>>
    {
        #region Fileds
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _Repository;
        private readonly IMapper _mapper;
        private readonly ICurrentUserService _currentUserService;
        #endregion

        #region Constructor(s)
        public GetQueryHandler(IRepositoryManager Repository, IMapper mapper, ILoggerManager logger , ICurrentUserService currentUserService)
        {
            _logger = logger;
            _Repository = Repository;
            _mapper = mapper;
            _currentUserService = currentUserService;
        }
        #endregion

        #region Functions
        public async Task<BaseResponse<ResolutionMemberVoteDto>> Handle(GetQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var memberId = request.MemberId is null  ?  await _Repository.BoardMembers.GetActiveBoardMemberByResolutionIdAsync(request.Id.Value, _currentUserService.UserId.Value) : request.MemberId;
                var result =await  _Repository.ResolutionMemberVotes.GetByMemeberAndResolutionAsync(request.Id.Value, memberId.Value, false);
                if (result == null)
                    return NotFound<ResolutionMemberVoteDto>("ResolutionMemberVote with this Id not found!");
                var resultMapper = _mapper.Map<ResolutionMemberVoteDto>(result);
                return Success(resultMapper);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error: in GetResultByIdQuery");
                return ServerError<ResolutionMemberVoteDto>(ex.Message);
            }
        }

        #endregion
    }
}
