using FluentValidation;
using Microsoft.Extensions.Localization;
using Resources;
using Microsoft.AspNetCore.Identity;
using Domain.Entities.Users;
using Application.Features.Identity.Users.Commands.ChangePassword;
using Abstraction.Contract.Service;

namespace Application.Features.Identity.Users.Validation
{
    /// <summary>
    /// Validator for ChangePasswordCommand
    /// Enhanced for Sprint 3 with conditional validation rules
    /// </summary>
    public class ChangePasswordValidator : AbstractValidator<ChangePasswordCommand>
    {
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly UserManager<User> _userManager;
        private readonly ICurrentUserService _currentUserService;

        public ChangePasswordValidator(
            IStringLocalizer<SharedResources> localizer,
            UserManager<User> userManager,
            ICurrentUserService currentUserService)
        {
            _localizer = localizer;
            _userManager = userManager;
            _currentUserService = currentUserService;
            ApplyValidationRules();
        }

        private void ApplyValidationRules()
        {

            // Current password validation (conditional)
            RuleFor(x => x.CurrentPassword)
                .NotEmpty()
                .WithMessage(_localizer[SharedResourcesKey.PasswordIncorrectCurrent])
                .MustAsync(async (command, currentPassword, cancellationToken) => await IsCurrentPasswordValid(command, currentPassword))
                .WithMessage(_localizer[SharedResourcesKey.PasswordIncorrectCurrent])
                .When(x => !string.IsNullOrEmpty(x.CurrentPassword));

            // New password validation
            RuleFor(x => x.NewPassword)
                .NotEmpty()
                .WithMessage(_localizer[SharedResourcesKey.ProfileRequiredField])
                .MinimumLength(8)
                .WithMessage(_localizer[SharedResourcesKey.PasswordMinimumLength])
                .Matches(@"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]")
                .WithMessage(_localizer[SharedResourcesKey.PasswordComplexityError]);


            // Confirm password validation
            RuleFor(x => x.ConfirmPassword)
                .NotEmpty()
                .WithMessage(_localizer[SharedResourcesKey.ProfileRequiredField])
                .Equal(x => x.NewPassword)
                .WithMessage(_localizer[SharedResourcesKey.PasswordMismatch]);

            // Custom validation: New password should not be same as current
            RuleFor(x => x)
                .MustAsync(async (command, cancellationToken) => await NewPasswordDifferentFromCurrent(command))
                .WithMessage(_localizer[SharedResourcesKey.PasswordSameAsCurrent])
                .When(x => !string.IsNullOrEmpty(x.CurrentPassword) && !string.IsNullOrEmpty(x.NewPassword));
        }

        private async Task<bool> IsCurrentPasswordValid(ChangePasswordCommand command, string currentPassword)
        {
            try
            {
                if (string.IsNullOrEmpty(currentPassword))
                    return false;

                var currentUserId = _currentUserService.UserId;
                if (!currentUserId.HasValue)
                    return false;

                var user = await _userManager.FindByIdAsync(currentUserId.Value.ToString());
                if (user == null)
                    return false;

                var passwordVerificationResult = _userManager.PasswordHasher.VerifyHashedPassword(
                    user, user.PasswordHash!, currentPassword);

                return passwordVerificationResult == PasswordVerificationResult.Success;
            }
            catch
            {
                return false;
            }
        }

        private async Task<bool> NewPasswordDifferentFromCurrent(ChangePasswordCommand command)
        {
            try
            {
                if (string.IsNullOrEmpty(command.CurrentPassword) || string.IsNullOrEmpty(command.NewPassword))
                    return true;

                var currentUserId = _currentUserService.UserId;
                if (!currentUserId.HasValue)
                    return true;

                var user = await _userManager.FindByIdAsync(currentUserId.Value.ToString());
                if (user == null)
                    return true;

                // Check if the new password is the same as current password
                var passwordVerificationResult = _userManager.PasswordHasher.VerifyHashedPassword(
                    user, user.PasswordHash!, command.NewPassword);

                return passwordVerificationResult == PasswordVerificationResult.Failed;
            }
            catch
            {
                return true; // Allow if we can't verify
            }
        }
    }
}
