﻿using AutoMapper;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Application.Features.ResolutionMemberVotes.Dtos;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Abstraction.Contract.Service;


namespace Application.Features.ResolutionMemberVotes.Queries.Get
{
    public class GetResolutionMembersQueryHandler : BaseResponse<PERSON>andler, IQueryHandler<GetResolutionMembersQuery, BaseResponse<ResolutionMembersDto>>
    {
        #region Fileds
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _Repository;
        private readonly IMapper _mapper;
        private readonly ICurrentUserService _currentUserService;
        #endregion

        #region Constructor(s)
        public GetResolutionMembersQueryHandler(IRepositoryManager Repository, IMapper mapper, ILoggerManager logger , ICurrentUserService currentUserService)
        {
            _logger = logger;
            _Repository = Repository;
            _mapper = mapper;
            _currentUserService = currentUserService;
        }
        #endregion

        #region Functions
        public async Task<BaseResponse<ResolutionMembersDto>> Handle(GetResolutionMembersQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var result =await  _Repository.ResolutionMemberVotes.GetMembersByResolutionIdAsync(request.Id.Value, false);
                if (result == null)
                    return NotFound<ResolutionMembersDto>("ResolutionMemberVote with this Id not found!");
                var resolutionMembers = _mapper.Map<List<ResolutionMemberDto>>(result);
                var resultMapper = new ResolutionMembersDto();
                 resultMapper.Members = resolutionMembers;
                return Success(resultMapper);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error: in GetResultByIdQuery");
                return ServerError<ResolutionMembersDto>(ex.Message);
            }
        }
        #endregion
    }
}
