using AutoMapper;
using Application.Common.Helpers;
using Domain.Entities.ResolutionManagement;
using Domain.Entities.FundManagement;
using Microsoft.Extensions.Localization;
using Resources;
using Domain.Services;

namespace Application.Mapping.Resolutions
{
    /// <summary>
    /// Custom AutoMapper value resolver for localized resolution status display
    /// Provides localized text for resolution status using SharedResources
    /// </summary>
    public class ResolutionStatusDisplayResolver : IValueResolver<Resolution, object, string>
    {
        private readonly IStringLocalizer<SharedResources> _localizer;

        public ResolutionStatusDisplayResolver(IStringLocalizer<SharedResources> localizer)
        {
            _localizer = localizer;
        }

        public string Resolve(Resolution source, object destination, string destMember, ResolutionContext context)
        {
            return LocalizationHelper.GetResolutionStatusDisplay(source.Status, _localizer);
        }
    }

    /// <summary>
    /// Custom AutoMapper value resolver for localized voting type display
    /// Provides localized text for voting type using SharedResources
    /// </summary>
    public class VotingTypeDisplayResolver : IValueResolver<Resolution, object, string>
    {
        private readonly IStringLocalizer<SharedResources> _localizer;

        public VotingTypeDisplayResolver(IStringLocalizer<SharedResources> localizer)
        {
            _localizer = localizer;
        }

        public string Resolve(Resolution source, object destination, string destMember, ResolutionContext context)
        {
            return LocalizationHelper.GetVotingTypeDisplay(source.VotingType, _localizer);
        }
    }

    /// <summary>
    /// Custom AutoMapper value resolver for localized member voting result display
    /// Provides localized text for member voting result using SharedResources
    /// </summary>
     
    public class ItemVotingResultDisplayResolver : IValueResolver<ItemVotingResult, object, string>
    {
        private readonly IStringLocalizer<SharedResources> _localizer;

        public ItemVotingResultDisplayResolver(IStringLocalizer<SharedResources> localizer)
        {
            _localizer = localizer;
        }

        public string Resolve(ItemVotingResult source, object destination, string destMember, ResolutionContext context)
        {
            return LocalizationHelper.GetItemVoteValueDisplay(source.VoteResult, _localizer);
        }
    }
    public class MemberItemVotingResultDisplayResolver : IValueResolver<ResolutionItemVote, object, string>
    {
        private readonly IStringLocalizer<SharedResources> _localizer;

        public MemberItemVotingResultDisplayResolver(IStringLocalizer<SharedResources> localizer)
        {
            _localizer = localizer;
        }

        public string Resolve(ResolutionItemVote source, object destination, string destMember, ResolutionContext context)
        {
            return LocalizationHelper.GetMemberVoteValueDisplay(source.VoteResult, _localizer);
        }
    }
    public class BoardMemberTypeDisplayResolver : IValueResolver<ResolutionMemberVote, object, string>
    {
        private readonly IStringLocalizer<SharedResources> _localizer;

        public BoardMemberTypeDisplayResolver(IStringLocalizer<SharedResources> localizer)
        {
            _localizer = localizer;
        }

        public string Resolve(ResolutionMemberVote source, object destination, string destMember, ResolutionContext context)
        {
            return LocalizationHelper.GetBoardMemberTypeDisplay(source.BoardMember.MemberType, _localizer);
        }
    }
    public class MemberVotingResultDisplayResolver : IValueResolver<ResolutionMemberVote, object, string>
    {
        private readonly IStringLocalizer<SharedResources> _localizer;

        public MemberVotingResultDisplayResolver(IStringLocalizer<SharedResources> localizer)
        {
            _localizer = localizer;
        }

        public string Resolve(ResolutionMemberVote source, object destination, string destMember, ResolutionContext context)
        {
            var memberVoteResult = VotingDomainService.CalculateResolutionMemberVoteResult(source.ResolutionItemVotes.ToList(), source.Resolution.MemberVotingResult);
            return LocalizationHelper.GetMemberVoteValueDisplay(memberVoteResult, _localizer);
        }
    }
}
