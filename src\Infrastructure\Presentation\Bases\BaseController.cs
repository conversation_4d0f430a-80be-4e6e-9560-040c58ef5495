﻿using Abstraction.Base.Dto;
using Abstraction.Base.Response;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Net;
using Microsoft.AspNetCore.Authorization;
using Abstraction.Contracts.Service;
using Abstraction.Common.Wappers;


namespace Presentation.Bases
{
    [ApiController]
   // [Authorize]
    [Produces("application/json")]
    [Consumes("application/json")]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public class BaseController : ControllerBase  
    {


        public ObjectResult NewResult<T>(BaseResponse<T> response)
        {
            switch (response.StatusCode)
            {
                case HttpStatusCode.OK:
                    return new OkObjectResult(response);
                case HttpStatusCode.Created:
                    return new OkObjectResult(response);
                case HttpStatusCode.Accepted:
                    return new AcceptedResult();
                case HttpStatusCode.BadRequest:
                    return new BadRequestObjectResult(response);
                case HttpStatusCode.Unauthorized:
                    return new UnauthorizedObjectResult(response);
                case HttpStatusCode.NotFound:
                    return new NotFoundObjectResult(response);
                case HttpStatusCode.Conflict:
                    return new ConflictObjectResult(response);
                case HttpStatusCode.InternalServerError:
                    return new ObjectResult(response) { StatusCode = StatusCodes.Status500InternalServerError };
                default:
                    return new ObjectResult(response) { StatusCode = (int)response.StatusCode };
            }
        }

    }

    /// <summary>
    /// Generic base controller providing standard CRUD operations for service-based architecture
    /// </summary>
    /// <typeparam name="TEntity">The domain entity type</typeparam>
    /// <typeparam name="TDto">The DTO type for the entity</typeparam>
    [ApiController]
    [Produces("application/json")]
    [Consumes("application/json")]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public class BaseController<TEntity, TDto> : BaseController
        where TEntity : class
        where TDto : BaseDto
    {
        protected readonly IBaseService<TEntity> _service;
        protected readonly IAuthorizationService _authorizationService;

        public BaseController(IBaseService<TEntity> service, IAuthorizationService authorizationService)
        {
            _service = service;
            _authorizationService = authorizationService;
        }

        /// <summary>
        /// Get entity by ID
        /// </summary>
        /// <param name="id">Entity ID</param>
        /// <returns>Entity DTO</returns>
        [HttpGet("{id:int}")]
        public virtual async Task<IActionResult> GetById(int id)
        {
            var response = await _service.GetByIdAsync<TDto>(id, false);
            return NewResult(response);
        }

        /// <summary>
        /// Get paginated list of entities
        /// </summary>
        /// <param name="pageNumber">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="orderBy">Order by field</param>
        /// <returns>Paginated list of entities</returns>
        [HttpGet]
        public virtual async Task<IActionResult> GetAll([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10, [FromQuery] string? orderBy = null)
        {
            var response = await _service.GetAllPagedAsync<TDto>(pageNumber, pageSize, orderBy, false);
            return Ok(response);
        }

        /// <summary>
        /// Create new entity
        /// </summary>
        /// <param name="dto">Entity DTO</param>
        /// <returns>Success message</returns>
        [HttpPost]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status422UnprocessableEntity)]
        public virtual async Task<IActionResult> Create([FromBody] TDto dto)
        {
            var response = await _service.AddAsync(dto);
            return NewResult(response);
        }

        /// <summary>
        /// Update existing entity
        /// </summary>
        /// <param name="dto">Entity DTO</param>
        /// <returns>Success message</returns>
        [HttpPut]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status422UnprocessableEntity)]
        public virtual async Task<IActionResult> Update([FromBody] TDto dto)
        {
            var response = await _service.UpdateAsync(dto);
            return NewResult(response);
        }

        /// <summary>
        /// Delete entity by ID
        /// </summary>
        /// <param name="id">Entity ID</param>
        /// <returns>Success message</returns>
        [HttpDelete("{id:int}")]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status422UnprocessableEntity)]
        public virtual async Task<IActionResult> Delete(int id)
        {
            var response = await _service.DeleteAsync(id);
            return NewResult(response);
        }
    }
}
