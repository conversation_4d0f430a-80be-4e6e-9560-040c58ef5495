﻿using Abstraction.Contracts.Logger;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using AutoMapper;
using Domain.Entities.ResolutionManagement;
using Abstraction.Contracts.Repository;
using Abstraction.Contract.Service;
using Application.Features.ResolutionMemberVotes.Dtos;

namespace Application.Features.ResolutionMemberVotes.Commands.Edit
{
    public class EditResolutionMemberVoteCommandHandler : BaseResponseHand<PERSON>, ICommandHandler<EditResolutionMemberVoteCommand, BaseResponse<string>>
    {

        #region Fileds
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _Repository;
        private readonly IMapper _mapper;
        private readonly ICurrentUserService _currentUserService;
        #endregion

        #region Constructors
        public EditResolutionMemberVoteCommandHandler(IRepositoryManager Repository, IMapper mapper, ILoggerManager logger, ICurrentUserService currentUserService)
        {
            _logger = logger;
            _Repository = Repository;
            _mapper = mapper;
            _currentUserService = currentUserService;
        }
        #endregion

        #region Handle Functions
        public async Task<BaseResponse<string>> Handle(EditResolutionMemberVoteCommand request, CancellationToken cancellationToken)
        {
            try
            {
                if (request == null)
                    return BadRequest<string>("the request can't be blank");
                var memberId = await _Repository.BoardMembers.GetActiveBoardMemberByResolutionIdAsync(request.ResolutionId, _currentUserService.UserId.Value);
                var originalEntity = await _Repository.ResolutionMemberVotes.GetByMemeberAndResolutionAsync(request.ResolutionId, memberId, true); // Enable tracking for updates
                
                // Handle collection updates manually to avoid foreign key constraint issues
               await UpdateResolutionItemVotes(originalEntity, request.ItemsVote);
               await UpdateResolutionVoteComments(originalEntity, request.VoteComments);
                
                // Update other properties excluding collections
                request.CreatedBy = originalEntity.CreatedBy; // Keep original creator  
                request.CreatedAt = originalEntity.CreatedAt; // Keep original creation time
                _mapper.Map(request, originalEntity);
                
                var status = await _Repository.ResolutionMemberVotes.UpdateAsync(originalEntity);
                if (!status)
                    return BadRequest<string>("Update Operation Failed.");
                return Success("Update Operation Successfully.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error: in EditResolutionMemberVoteCommand");
                return ServerError<string>(ex.Message);
            }
        }

        private async Task UpdateResolutionItemVotes(ResolutionMemberVote originalEntity, List<ResolutionItemVoteDto>? itemsVote)
        {          
            // Update existing and add new items
            foreach (var itemVoteDto in itemsVote)
            {
                var existingItem = originalEntity.ResolutionItemVotes.FirstOrDefault(x => x.Id == itemVoteDto.Id);
                
                //Edit ItemVote 
                if (existingItem != null)
                {
                    // Update existing item
                    existingItem.VoteResult = itemVoteDto.VoteResult;
                    itemVoteDto.CreatedBy = existingItem.CreatedBy; // Keep original creator
                    itemVoteDto.CreatedAt = existingItem.CreatedAt; // Keep original creation time
                }
                else
                {
                    // Add new item
                    var newItem = new ResolutionItemVote
                    {
                        ResolutionItemId = itemVoteDto.ResolutionItemId,
                        VoteResult = itemVoteDto.VoteResult,
                    };
                    originalEntity.ResolutionItemVotes.Add(newItem);
                }

                foreach (var itemVoteCommentDto in itemVoteDto.ItemComments)
                {
                    var existingComment = existingItem.ResolutionItem.ResolutionItemVoteComments.FirstOrDefault(x => x.Id == itemVoteCommentDto.Id);
                    //The UPDATE statement conflicted with the FOREIGN KEY constraint "FK_ResolutionMemberVoteComments_AspNetUsers_CreatedBy".
                   //     The conflict occurred in database "Jadwa_3", table "dbo.AspNetUsers", column 'Id'.
                    if (existingComment != null)
                    {
                      //  existingComment.CreatedByUser = null;
                        // Update existing comment
                        existingComment.Comment = itemVoteCommentDto.Comment;
                        itemVoteCommentDto.CreatedBy = existingComment.CreatedBy; // Keep original creator
                        itemVoteCommentDto.CreatedAt = existingComment.CreatedAt; // Keep original creation time
                    }
                    else
                    {
                        // Add new comment
                        var newComment = new ResolutionItemVoteComment
                        {
                            Comment = itemVoteCommentDto.Comment,
                            ResolutionItemID = existingItem.ResolutionItemId,
                            BoardMemberID = existingItem.ResolutionMemberVote.BoardMemberID,
                        };
                        existingItem.ResolutionItem.ResolutionItemVoteComments.Add(newComment);
                    }
                }
            }
        }

        private async Task UpdateResolutionVoteComments(ResolutionMemberVote originalEntity, List<ResolutionVoteCommentDto>? voteComments)
        {
            // Update existing and add new comments
            foreach (var commentDto in voteComments)
            {
                var existingComment = originalEntity.ResolutionMemberVoteComments.FirstOrDefault(x => x.Id == commentDto.Id);
                
                if (existingComment != null)
                {
                  //  existingComment.CreatedByUser = null;
                    // Update existing comment
                    existingComment.Comment = commentDto.Comment;
                    commentDto.CreatedBy = existingComment.CreatedBy; // Keep original creator
                    commentDto.CreatedAt = existingComment.CreatedAt; // Keep original creation time
                }
                else
                {
                    // Add new comment
                    var newComment = new ResolutionMemberVoteComment
                    {
                        Comment = commentDto.Comment,
                        ResolutionMemberVoteID = originalEntity.Id,
                    };
                    originalEntity.ResolutionMemberVoteComments.Add(newComment);
                }
            }
        }

        #endregion

    }
}
