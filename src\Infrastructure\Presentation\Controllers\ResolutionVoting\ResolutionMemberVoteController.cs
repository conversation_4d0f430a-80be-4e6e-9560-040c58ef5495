﻿using Microsoft.AspNetCore.Mvc;
using Application.Features.ResolutionMemberVotes.Commands.Edit;
using Application.Features.ResolutionMemberVotes.Queries.Get;
using Presentation.Bases;
using Abstraction.Base.Response;
using Microsoft.AspNetCore.Http;
using Application.Features.ResolutionMemberVotes.Dtos;
using DocumentFormat.OpenXml.Office2010.Excel;
using Domain.Entities.ResolutionManagement;
using Application.Features.ResolutionMemberVotes.Queries.GetResolutionMembersByResult;


namespace Presentation.Controllers.ResolutionVoting
{
    [Route("api/[controller]")]
    [ApiController]
    public class ResolutionMemberVoteController : AppControllerBase
    {
        [HttpGet("ViewMemberVote")]
        [ProducesResponseType(typeof(BaseResponse<ResolutionMemberVoteDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetResolutionMemberVotesById(int? id, int? memberID)
        {
            var response = await Mediator.Send(new GetQuery() { Id = id, MemberId = memberID });
            return NewResult(response);
        }

        [HttpPut("SubmitVote")]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status422UnprocessableEntity)]
        public async Task<IActionResult> EditResolutionMemberVote([FromBody] EditResolutionMemberVoteCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }
        [HttpGet("ResolutionMembers")]
        [ProducesResponseType(typeof(BaseResponse<ResolutionMembersDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetResolutionMembers(int? id)
        {
            var response = await Mediator.Send(new GetResolutionMembersQuery() { Id = id});
            return NewResult(response);
        }
        [HttpGet("MemberVote")]
        [ProducesResponseType(typeof(BaseResponse<ResolutionMemberVoteResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetResolutionMemberVote(int? id, int? memberID)
        {
            var response = await Mediator.Send(new GetResolutionMemberVoteQuery() { Id = id.Value , MemberId = memberID.Value });
            return NewResult(response);
        }

        [HttpGet("ResolutionItemMembersByVoteResult")]
        [ProducesResponseType(typeof(BaseResponse<List<string>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ResolutionItemMembersByVoteResult(int? id, ItemVoteResult itemVoteResult)
        {
            var response = await Mediator.Send(new GetResolutionMembersByResultQuery() { Id = id.Value, ItemVoteResult = itemVoteResult });
            return NewResult(response);
        }
    }
}
