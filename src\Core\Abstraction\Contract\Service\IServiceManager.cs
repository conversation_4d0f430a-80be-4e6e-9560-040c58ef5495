﻿using Abstraction.Contract.Service.ResolutionVoting;
using Abstraction.Contract.Service.Storage;
using Abstraction.Contracts.Service.Catalog;

namespace Abstraction.Contracts.Service
{
    public interface IServiceManager
    {
        IProductService ProductService { get; }
        IStorageService StorageService { get; }
        IPreviewUrlHelper PreviewUrlHelper { get; }

        // Resolution Voting Services
        IResolutionMemberVoteService ResolutionMemberVoteService { get; }
        IResolutionMemberVoteCommentService ResolutionMemberVoteCommentService { get; }
        IResolutionItemVoteCommentService ResolutionItemVoteCommentService { get; }
    }
}
