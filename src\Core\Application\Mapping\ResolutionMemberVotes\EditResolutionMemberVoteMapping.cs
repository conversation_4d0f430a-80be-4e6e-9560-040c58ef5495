﻿using Application.Features.ResolutionMemberVotes.Commands.Edit;
using Application.Features.ResolutionMemberVotes.Dtos;
using Application.Mapping.Resolutions;
using Domain.Entities.ResolutionManagement;

namespace Application.Mapping
{
    public partial class ResolutionMemberVotesProfile
    {
        public void EditResolutionMemberVoteMapping()
        {
            CreateMap<ResolutionMemberVote, ResolutionMemberVoteDto>()
             .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
             .ForMember(dest => dest.ResolutionId, opt => opt.MapFrom(src => src.ResolutionId))
             .ForMember(dest => dest.ItemsVote, opt => opt.MapFrom(src => src.ResolutionItemVotes))
             .ForMember(dest => dest.VoteComments, opt => opt.MapFrom(src => src.ResolutionMemberVoteComments))
             .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.CreatedBy))
             .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt))
             .ReverseMap();

            CreateMap<ResolutionItemVote, ResolutionItemVoteDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.ResolutionItemId, opt => opt.MapFrom(src => src.ResolutionItemId))
                .ForMember(dest => dest.VoteResult, opt => opt.MapFrom(src => src.VoteResult))
                .ForMember(dest => dest.VoteResultDisplay, opt => opt.MapFrom<MemberItemVotingResultDisplayResolver>())
                .ForMember(dest => dest.ItemComments, opt => opt.MapFrom(src => src.ResolutionItem.ResolutionItemVoteComments.Where(c => c.BoardMemberID == src.ResolutionMemberVote.BoardMemberID)))
                .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.CreatedBy))
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt))
                .ReverseMap();

            CreateMap<ResolutionItemVoteComment, ResolutionItemVoteCommentDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.Comment, opt => opt.MapFrom(src => src.Comment))
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt))
                .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.CreatedBy))
                .ReverseMap();

            CreateMap<ResolutionMemberVoteComment, ResolutionVoteCommentDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.Comment, opt => opt.MapFrom(src => src.Comment))
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt))
                .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.CreatedBy))
                .ReverseMap();

        }
    }
}
