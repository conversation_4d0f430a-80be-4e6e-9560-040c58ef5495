using Abstraction.Contract.Service.ResolutionVoting;
using Abstraction.Contracts.Repository;
using AutoMapper;
using Domain.Entities.ResolutionManagement;
using Infrastructure.Service;
using Microsoft.Extensions.Localization;
using Resources;

namespace Infrastructure.Onion.Service.ResolutionVoting
{
    /// <summary>
    /// Service implementation for ResolutionMemberVote operations
    /// Inherits from BaseService to provide standard CRUD operations
    /// Implements IResolutionMemberVoteService interface
    /// Follows the established service implementation pattern in the project
    /// </summary>
    public class ResolutionMemberVoteService : BaseService<ResolutionMemberVote>, IResolutionMemberVoteService
    {
        /// <summary>
        /// Initializes a new instance of the ResolutionMemberVoteService class
        /// </summary>
        /// <param name="repository">Generic repository for data access operations</param>
        /// <param name="mapper">AutoMapper instance for object mapping</param>
        /// <param name="localizer">String localizer for localization support</param>
        public ResolutionMemberVoteService(
            IGenericRepository repository,
            IMapper mapper,
            IStringLocalizer<SharedResources> localizer)
            : base(repository, mapper, localizer)
        {
        }

        // Additional ResolutionMemberVote-specific methods can be implemented here if needed
        // For example:
        // public async Task<BaseResponse<bool>> HasMemberVotedAsync(int resolutionId, int boardMemberId)
        // {
        //     // Implementation for checking if a member has voted on a resolution
        // }
    }
}
