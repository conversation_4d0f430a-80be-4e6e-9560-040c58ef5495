﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Domain.Entities.FundManagement;
using Domain.Entities.ResolutionManagement;

namespace Application.Features.ResolutionMemberVotes.Dtos
{
    public record ResolutionMembersDto 
    {
        public  List<ResolutionMemberDto> Members { get; set; }
        public int AcceptedMembersCount
        {
            get
            {
                return Members.Count(m => m.VoteResult == VoteResult.Accept);
            }
        }
        public int RejectedMembersCount
        {
            get
            {
                return Members.Count(m => m.VoteResult == VoteResult.Reject);
            }
        }
        public int PendingMembersCount
        {
            get
            {
                return Members.Count(m => m.VoteResult == VoteResult.NotVotedYet);
            }
        }
        
    }
}
