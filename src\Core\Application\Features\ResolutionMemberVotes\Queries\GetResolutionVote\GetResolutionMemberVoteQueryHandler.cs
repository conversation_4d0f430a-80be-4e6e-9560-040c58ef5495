﻿using AutoMapper;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Abstraction.Contract.Service;
using Application.Features.ResolutionMemberVotes.Dtos;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Microsoft.Extensions.Localization;
using Resources;

namespace Application.Features.ResolutionMemberVotes.Queries.Get
{
    /// <summary>
    /// Handler for GetQuery to retrieve resolution details
    /// Implements role-based access control and comprehensive resolution information
    /// Based on Sprint.md requirements (JDWA-588, JDWA-584, JDWA-593, JDWA-589)
    /// </summary>
    public class GetResolutionMemberVoteQueryHandler : BaseResponseHandler, IQueryHandler<GetResolutionMemberVoteQuery, BaseResponse<SingleResolutionResponseView>>
    {
        #region Fields
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _repository;
        private readonly IMapper _mapper;
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly ICurrentUserService _currentUserService;
        #endregion

        #region Constructor(s)
        public GetResolutionMemberVoteQueryHandler(
            IRepositoryManager repository,
            IMapper mapper,
            ILoggerManager logger,
            IStringLocalizer<SharedResources> localizer,
            ICurrentUserService currentUserService)
        {
            _logger = logger;
            _repository = repository;
            _mapper = mapper;
            _localizer = localizer;
            _currentUserService = currentUserService;
        }
        #endregion

        #region Functions
        public async Task<BaseResponse<SingleResolutionResponseView>> Handle(GetResolutionMemberVoteQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInfo($"Starting GetResolution operation for ID: {request.Id}");

                // 1. Validate request
                if (request.Id <= 0)
                    return BadRequest<SingleResolutionResponseView>(_localizer[SharedResourcesKey.InvalidIdValidation]);

                // 2. Get current user information for role-based access control
                var currentUserId = _currentUserService.UserId;
                var currentUserRoles = _currentUserService.Roles;

                // 3. Retrieve resolution with related data
                var resolution =  await _repository.Resolutions.GetResolutionVote(request.Id,request.MemberId, trackChanges: false);
                if (resolution == null)
                {
                    _logger.LogWarn($"Resolution not found with ID: {request.Id}");
                    return NotFound<SingleResolutionResponseView>(_localizer[SharedResourcesKey.ResolutionNotFound]);
                }
                // 6. Build comprehensive resolution response
                var response =   _mapper.Map<SingleResolutionResponseView>(resolution);
                _logger.LogInfo($"Resolution details retrieved successfully for ID: {request.Id}");
                return Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in GetResolution for ID: {request.Id}");
                return ServerError<SingleResolutionResponseView>(_localizer[SharedResourcesKey.SystemErrorSavingData]);
            }
        }
        #endregion
    }
}
