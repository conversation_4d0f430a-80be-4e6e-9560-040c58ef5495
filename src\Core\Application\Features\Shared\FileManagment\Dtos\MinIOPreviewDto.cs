﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Features.Shared.FileManagment.Dtos
{
    public record MinIOPreviewDto
    {
        /// <summary>
        /// Attachment ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Original file name
        /// </summary>
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// Presigned URL for file preview/access
        /// </summary>
        public string PreviewUrl { get; set; } = string.Empty;

        /// <summary>
        /// URL expiry date and time
        /// </summary>
        public DateTime ExpiresAt { get; set; }

        /// <summary>
        /// File content type
        /// </summary>
        public string ContentType { get; set; } = string.Empty;

        /// <summary>
        /// File size in bytes
        /// </summary>
        public long FileSize { get; set; }
    }
}
