using FluentValidation;
using Microsoft.Extensions.Localization;
using Resources;
using Abstraction.Contracts.Identity;
using Abstraction.Constants;
using Application.Features.Identity.Users.Commands.EditUser;
using Abstraction.Contracts.Repository;
using Domain.Entities.Shared;

namespace Application.Features.Identity.Users.Validation
{
    /// <summary>
    /// Validator for EditUserCommand with Sprint 3 enhancements
    /// Implements mobile number validation and unique role checking with current user exclusion
    /// </summary>
    public class EditUserValidator : AbstractValidator<EditUserCommand>
    {
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly IRepositoryManager _iRepositoryManager;
        private readonly IIdentityServiceManager _identityServiceManager;

        public EditUserValidator(
            IStringLocalizer<SharedResources> localizer,
            IRepositoryManager iRepositoryManager,
            IIdentityServiceManager identityServiceManager)
        {
            _localizer = localizer;
            _iRepositoryManager = iRepositoryManager;
            _identityServiceManager = identityServiceManager;
            Include(new BaseUserValidator(_localizer));
            ApplyValidationRules();
        }

        private void ApplyValidationRules()
        {
            // User ID Validation
            RuleFor(x => x.Id)
                .GreaterThan(0)
                .WithMessage(_localizer[SharedResourcesKey.ProfileRequiredField]);

            // Role Validation - Basic requirements
            RuleFor(x => x.Roles)
                .NotEmpty()
                .WithMessage(_localizer[SharedResourcesKey.AtLeastOneRoleRequired])
                .Must(roles => roles.Count > 0)
                .WithMessage(_localizer[SharedResourcesKey.AtLeastOneRoleRequired]);

            // Role Selection Logic Validation
            RuleFor(x => x.Roles)
                .Must(BeValidRoleSelection)
                .WithMessage(_localizer[SharedResourcesKey.EditUserInvalidRoleSelection]);

            // Board Member Role Change Restriction
            RuleFor(x => x)
                .MustAsync(async (command, cancellation) =>
                {
                    var user = await _identityServiceManager.UserManagmentService.FindByIdWithAttachmentAndRolesAsync(command.Id.ToString());

                    var hasBoardMemberRole = user.Roles.Select(c => c.Name).Contains(RoleHelper.BoardMember);
                    var willRemoveBoardMemberRole = hasBoardMemberRole && !command.Roles.Contains(RoleHelper.BoardMember);

                    if (willRemoveBoardMemberRole)
                    {
                        var canRemove = await _iRepositoryManager.BoardMembers.CanRemoveBoardMemberRoleAsync(command.Id);
                        return canRemove;
                    }

                    return true;
                })
                .WithMessage(_localizer[SharedResourcesKey.EditUserCannotChangeBoardMemberRole]);

            // Fund Manager Role Change Restriction
            RuleFor(x => x)
                .MustAsync(async (command, cancellation) =>
                {
                    var user = await _identityServiceManager.UserManagmentService.FindByIdWithAttachmentAndRolesAsync(command.Id.ToString());
                    var hasFundManagerRole = user.Roles.Select(c => c.Name).Contains(RoleHelper.FundManager);
                    var willRemoveFundManagerRole = hasFundManagerRole && !command.Roles.Contains(RoleHelper.FundManager);

                    if (willRemoveFundManagerRole)
                    {
                        var canRemove = await _iRepositoryManager.FundManagers.CanRemoveFundManagerRoleAsync(command.Id);
                        return canRemove;
                    }

                    return true;
                })
                .WithMessage(string.Format(_localizer[SharedResourcesKey.EditUserCannotChangeFundManagerRole], "Fund Manager"));

            // Associate Fund Manager Role Change Restriction
            //RuleFor(x => x)
            //    .MustAsync(async (command, cancellation) =>
            //    {                  
            //        var user = await _identityServiceManager.UserManagmentService.FindByIdWithRolesAsync(command.Id.ToString());
            //        var hasAssociateFundManagerRole = user.Roles.Select(c=>c.Name).Contains(RoleHelper.AssociateFundManager);
            //        var willRemoveAssociateFundManagerRole = hasAssociateFundManagerRole && !command.Roles.Contains(RoleHelper.AssociateFundManager);

            //        if (willRemoveAssociateFundManagerRole)
            //        {
            //            var (canRemove, _) = await _fundAssignmentService.CanRemoveAssociateFundManagerRoleAsync(command.Id);
            //            return canRemove;
            //        }

            //        return true;
            //    })
            //    .WithMessage(string.Format(_localizer[SharedResourcesKey.EditUserCannotChangeFundManagerRole], "Associate Fund Manager"));

            // File Validation
            // CV file validation
            RuleFor(x => x.CvFileId)
                .MustAsync(async (cvFileId, cancellation) =>
                {
                    if (cvFileId <= 0) return true; // Optional attachment

                    var attachment = await _iRepositoryManager.Attachments.GetByIdAsync<Attachment>(cvFileId.Value, trackChanges: false);
                    if (attachment == null) return true; // Let attachment existence validation handle this

                    return attachment.FileName.EndsWith(".pdf", StringComparison.OrdinalIgnoreCase) ||
                            attachment.FileName.EndsWith(".doc", StringComparison.OrdinalIgnoreCase) ||
                            attachment.FileName.EndsWith(".docx", StringComparison.OrdinalIgnoreCase);
                })
                .WithMessage(_localizer[SharedResourcesKey.InvalidFileType])
                .When(x => x.CvFileId > 0);

            // Business rule: Attachment file size must not exceed 10 MB (Sprint.md requirement)
            RuleFor(x => x.CvFileId)
                .MustAsync(async (cvFileId, cancellation) =>
                {
                    if (cvFileId <= 0) return true; // Optional attachment

                    var attachment = await _iRepositoryManager.Attachments.GetByIdAsync<Attachment>(cvFileId.Value, trackChanges: false);
                    if (attachment == null) return true; // Let attachment existence validation handle this

                    // Check file size - 10 MB = 10 * 1024 * 1024 bytes
                    const long maxFileSizeBytes = 10 * 1024 * 1024;
                    return attachment.FileSize <= maxFileSizeBytes;
                })
                .WithMessage(_localizer[SharedResourcesKey.FileSizeExceedsLimit])
                .When(x => x.CvFileId > 0);

            // Personal photo file validation
            RuleFor(x => x.PersonalPhotoFileId)
                .MustAsync(async (personalPhotoFileId, cancellation) =>
                {
                    if (personalPhotoFileId <= 0) return true; // Optional attachment

                    var attachment = await _iRepositoryManager.Attachments.GetByIdAsync<Attachment>(personalPhotoFileId.Value, trackChanges: false);
                    if (attachment == null) return true; // Let attachment existence validation handle this

                    return attachment.FileName.EndsWith(".jpg", StringComparison.OrdinalIgnoreCase) ||
                            attachment.FileName.EndsWith(".jpeg", StringComparison.OrdinalIgnoreCase) ||
                            attachment.FileName.EndsWith(".png", StringComparison.OrdinalIgnoreCase);
                })
                .WithMessage(_localizer[SharedResourcesKey.InvalidFileType])
                .When(x => x.PersonalPhotoFileId > 0);

            // Business rule: Personal photo file size must not exceed 2 MB
            RuleFor(x => x.PersonalPhotoFileId)
                .MustAsync(async (personalPhotoFileId, cancellation) =>
                {
                    if (personalPhotoFileId <= 0) return true; // Optional attachment

                    var attachment = await _iRepositoryManager.Attachments.GetByIdAsync<Attachment>(personalPhotoFileId.Value, trackChanges: false);
                    if (attachment == null) return true; // Let attachment existence validation handle this

                    // Check file size - 2 MB = 2 * 1024 * 1024 bytes
                    const long maxFileSizeBytes = 2 * 1024 * 1024;
                    return attachment.FileSize <= maxFileSizeBytes;
                })
                .WithMessage(_localizer[SharedResourcesKey.FileSizeExceedsLimit])
                .When(x => x.PersonalPhotoFileId > 0);
        }

        /// <summary>
        /// Validates role selection logic according to JDWA-1251 requirements
        /// Multi-select enabled ONLY IF roles are ('Fund Manager' AND 'Board Member') OR ('Associate Fund Manager' AND 'Board Member')
        /// Otherwise, single role selection only
        /// </summary>
        private bool BeValidRoleSelection(List<string> roles)
        {
            if (roles == null || roles.Count == 0)
                return false;

            // Single role is always valid
            if (roles.Count == 1)
                return true;

            // Multi-select is only allowed for specific combinations
            if (roles.Count == 2)
            {
                var hasValidCombination =
                    (roles.Contains(RoleHelper.FundManager) && roles.Contains(RoleHelper.BoardMember)) ||
                    (roles.Contains(RoleHelper.AssociateFundManager) && roles.Contains(RoleHelper.BoardMember));

                return hasValidCombination;
            }

            // More than 2 roles is not allowed
            return false;
        }


    }
}
