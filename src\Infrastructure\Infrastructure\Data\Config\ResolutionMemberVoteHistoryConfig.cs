using Domain.Entities.ResolutionManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Data.Config
{
    /// <summary>
    /// Entity Framework configuration for ResolutionMemberVoteStatusHistory entity
    /// Configures table structure, relationships, and constraints
    /// </summary>
    public class ResolutionMemberVoteHistoryConfig : IEntityTypeConfiguration<ResolutionMemberVoteStatusHistory>
    {
        public void Configure(EntityTypeBuilder<ResolutionMemberVoteStatusHistory> builder)
        {
            // Table configuration
            builder.ToTable("ResolutionMemberVoteStatusHistories");
            
            // Primary key
            builder.HasKey(x => x.Id);
            
            // Properties configuration
            builder.Property(x => x.StatusID)
                .IsRequired()
                .HasComment("Foreign key reference to ResolutionMemberVoteStatus entity");
                
            builder.Property(x => x.ResolutionMemberVoteID)
                .IsRequired()
                .HasComment("Foreign key reference to ResolutionMemberVote entity");
            
            // Relationships configuration
            builder.HasOne(x => x.Status)
                .WithMany()
                .HasForeignKey(x => x.StatusID)
                .OnDelete(DeleteBehavior.Restrict)
                .HasConstraintName("FK_ResolutionMemberVoteStatusHistories_Statuses");

            builder.HasOne(x => x.ResolutionMemberVote)
                .WithMany(v => v.ResolutionMemberVoteStatusHistories)
                .HasForeignKey(x => x.ResolutionMemberVoteID)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_ResolutionMemberVoteStatusHistories_Votes");
            
            // Indexes for performance
            builder.HasIndex(x => x.StatusID)
                .HasDatabaseName("IX_ResolutionMemberVoteStatusHistories_StatusId");

            builder.HasIndex(x => x.ResolutionMemberVoteID)
                .HasDatabaseName("IX_ResolutionMemberVoteStatusHistories_VoteId");

            builder.HasIndex(x => x.CreatedAt)
                .HasDatabaseName("IX_ResolutionMemberVoteStatusHistories_CreatedAt");
            
            // Audit fields configuration (inherited from FullAuditedEntity)
            builder.Property(x => x.CreatedAt)
                .IsRequired()
                .HasDefaultValueSql("GETUTCDATE()");
                
            builder.Property(x => x.UpdatedAt)
                .HasDefaultValueSql("GETUTCDATE()");
                
            builder.Property(x => x.IsDeleted)
                .HasDefaultValue(false);
                
            builder.Property(x => x.DeletedAt)
                .IsRequired(false);
            
            // Soft delete filter
            builder.HasQueryFilter(x => !x.IsDeleted.HasValue || !x.IsDeleted.Value);
        }
    }
}
