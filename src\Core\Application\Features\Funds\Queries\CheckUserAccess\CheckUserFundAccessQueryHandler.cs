using Abstraction.Base.Response;
using Abstraction.Constants;
using Abstraction.Contract.Service;
using Abstraction.Contracts.Repository;
using Domain.Entities.FundManagement;
using MediatR;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;

namespace Application.Features.Funds.Queries.CheckUserAccess
{
    /// <summary>
    /// Handler for checking if the logged-in user has valid access to view a specific fund
    /// Implements role-based access control similar to GetUserFundRole pattern used in resolution handlers
    /// </summary>
    public class CheckUserFundAccessQueryHandler : BaseResponseHandler, IRequestHandler<CheckUserFundAccessQuery, BaseResponse<UserFundAccessDto>>
    {
        #region Fields
        private readonly IRepositoryManager _repository;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILogger<CheckUserFundAccessQueryHandler> _logger;
        #endregion

        #region Constructor
        public CheckUserFundAccessQueryHandler(
            IRepositoryManager repository,
            ICurrentUserService currentUserService,
            ILogger<CheckUserFundAccessQueryHandler> logger)
        {
            _repository = repository;
            _currentUserService = currentUserService;
            _logger = logger;
        }
        #endregion

        #region Handle Method
        public async Task<BaseResponse<UserFundAccessDto>> Handle(CheckUserFundAccessQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation($"Checking user fund access for Fund ID: {request.FundId}");

                var currentUserId = _currentUserService.UserId;           
                var fundDetails = await _repository.Funds.ViewFundUsers(request.FundId, trackChanges: false);
                if (fundDetails == null)
                {
                    _logger.LogWarning($"Fund not found with ID: {request.FundId}");
                }

                var userRole = await GetUserFundRole(fundDetails, currentUserId.Value);
                
                var response = new UserFundAccessDto
                {
                    HasAccess = userRole != Roles.None,
                    UserRole = userRole
                };

                _logger.LogInformation($"User {currentUserId} access check for Fund {request.FundId}: HasAccess={response.HasAccess}, Role={userRole}");

                return Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error checking user fund access for Fund ID: {request.FundId}");
                return ServerError<UserFundAccessDto>(ex.Message);
            }
        }
        #endregion

        #region Private Methods

        /// <summary>
        /// Determines the current user's role within a specific fund context
        /// Checks FundBoardSecretary table, Fund.LegalCouncilId field, and FundManager table
        /// Follows the same pattern as used in resolution handlers
        /// </summary>
        /// <param name="fundDetails">Fund details with related entities</param>
        /// <param name="currentUserId">Current user ID to check roles for</param>
        /// <returns>User's role within the fund or Roles.None if no access</returns>
        private async Task<Roles> GetUserFundRole(Fund fundDetails, int currentUserId)
        {
            try
            {
                _logger.LogInformation($"Checking fund roles for User ID: {currentUserId} in Fund ID: {fundDetails.Id}");

                // 1. Check if user is Legal Council for the fund
                if (fundDetails.LegalCouncilId == currentUserId)
                {
                    _logger.LogInformation($"User ID: {currentUserId} is Legal Council for Fund ID: {fundDetails.Id}");
                    return Roles.LegalCouncil;
                }

                // 2. Check if user is a Fund Manager for the fund
                if (fundDetails.FundManagers != null && fundDetails.FundManagers.Count > 0)
                {
                    var isFundManager = fundDetails.FundManagers.Any(fm => fm.UserId == currentUserId);
                    if (isFundManager)
                    {
                        _logger.LogInformation($"User ID: {currentUserId} is Fund Manager for Fund ID: {fundDetails.Id}");
                        return Roles.FundManager;
                    }
                }

                // 3. Check if user is a Board Secretary for the fund
                if (fundDetails.FundBoardSecretaries != null && fundDetails.FundBoardSecretaries.Count > 0)
                {
                    var isBoardSecretary = fundDetails.FundBoardSecretaries.Any(bs => bs.UserId == currentUserId);
                    if (isBoardSecretary)
                    {
                        _logger.LogInformation($"User ID: {currentUserId} is Board Secretary for Fund ID: {fundDetails.Id}");
                        return Roles.BoardSecretary;
                    }
                }

                // 4. Check if user is a Board Member for the fund
                if (fundDetails.BoardMembers != null && fundDetails.BoardMembers.Count > 0)
                {
                    var isBoardMember = fundDetails.BoardMembers.Any(bm => bm.UserId == currentUserId);
                    if (isBoardMember)
                    {
                        _logger.LogInformation($"User ID: {currentUserId} is Board Member for Fund ID: {fundDetails.Id}");
                        return Roles.BoardMember;
                    }
                }

                _logger.LogInformation($"User ID: {currentUserId} has no roles in Fund ID: {fundDetails.Id}");
                return Roles.None;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error checking fund roles for User ID: {currentUserId} in Fund ID: {fundDetails.Id}");
                return Roles.None;
            }
        }
        #endregion
    }
}
