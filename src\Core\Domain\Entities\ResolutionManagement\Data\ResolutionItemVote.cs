using Domain.Entities.Base;
using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Entities.ResolutionManagement
{
    /// <summary>
    /// Represents a vote cast by a board member on a specific resolution item
    /// Inherits from FullAuditedEntity to provide comprehensive audit trail functionality
    /// Links individual resolution items to member votes with specific vote results
    /// </summary>
    public class ResolutionItemVote : FullAuditedEntity
    {
        /// <summary>
        /// Resolution item identifier that this vote is for
        /// Foreign key reference to ResolutionItem entity
        /// </summary>
        public int ResolutionItemId { get; set; }

        /// <summary>
        /// Resolution member vote identifier that this item vote belongs to
        /// Foreign key reference to ResolutionMemberVote entity
        /// </summary>
        public int ResolutionMemberVoteId { get; set; }

        /// <summary>
        /// The vote result for this specific resolution item
        /// Uses VoteResult enum (NotEligibleToVote = 0 , NotVotedYet = 1, Accept = 2, Reject = 3)
        /// </summary>
        public VoteResult VoteResult { get; set; }

        /// <summary>
        /// Navigation property to ResolutionItem entity
        /// Provides access to the resolution item being voted on
        /// </summary>
        [ForeignKey("ResolutionItemId")]
        public virtual ResolutionItem ResolutionItem { get; set; } = null!;

        /// <summary>
        /// Navigation property to ResolutionMemberVote entity
        /// Provides access to the parent member vote
        /// </summary>
        [ForeignKey("ResolutionMemberVoteId")]
        public virtual ResolutionMemberVote ResolutionMemberVote { get; set; } = null!;



        /// <summary>
        /// Checks if the member has voted on this item (not in NotVotedYet state)
        /// </summary>
        /// <returns>True if member has cast a vote on this item, false otherwise</returns>
        public bool HasVoted()
        {
            return VoteResult != VoteResult.NotVotedYet && VoteResult != VoteResult.NotEligibleToVote;
        }
 
    }
}
