﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Abstraction.Constants.ModulePermissions
{
    public static class ResolutionMemberVotePermission
    {
        public const string View = "ResolutionMemberVote.View";
        public const string List = "ResolutionMemberVote.List";
        public const string Create = "ResolutionMemberVote.Create";
        public const string Edit = "ResolutionMemberVote.Edit";
        public const string Delete = "ResolutionMemberVote.Delete";
    }
}
