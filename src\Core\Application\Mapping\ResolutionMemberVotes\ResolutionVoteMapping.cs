using Application.Features.ResolutionMemberVotes.Dtos;
using Application.Features.Resolutions.Dtos;
using Application.Mapping.Resolutions;
using Application.Mapping.Users;
using Domain.Entities.ResolutionManagement;
using System.Globalization;

namespace Application.Mapping
{
    /// <summary>
    /// Mapping configurations for retrieving Resolution entities
    /// Maps from domain entities to DTOs for read operations
    /// </summary>
    public partial class ResolutionMemberVotesProfile
    {
        public void ResolutionVoteMapping()
        {
            // Resolution entity to ResolutionDto


            // Resolution entity to SingleResolutionResponseView 
            CreateMap<Resolution, Features.ResolutionMemberVotes.Dtos.SingleResolutionResponseView>()
                .IncludeBase<Resolution, ResolutionDto>()
                .ForMember(dest => dest.FundName, opt => opt.MapFrom(src => src.Fund.Name))
                .ForMember(dest => dest.ResolutionMemberVote, opt => opt.MapFrom(src => src.ResolutionMemberVotes.FirstOrDefault()))
                .ForMember(dest => dest.ResolutionType, opt => opt.MapFrom(src => src.ResolutionType))
                .ForMember(dest => dest.ItemsCount, opt => opt.MapFrom(src => src.ResolutionItems.Count))
                .ForMember(dest => dest.LastUpdated, opt => opt.MapFrom(src => src.UpdatedAt ?? src.CreatedAt))
                .ForMember(dest => dest.StatusId, opt => opt.MapFrom(src => (int)src.Status))
                .ForMember(dest => dest.ResolutionStatus, opt => opt.MapFrom(src => src.ResolutionStatus))
                .ForMember(dest => dest.StatusDisplay, opt => opt.MapFrom<ResolutionStatusDisplayResolver>())
                .ForMember(dest => dest.VotingTypeDisplay, opt => opt.MapFrom<VotingTypeDisplayResolver>())
              
                // Enhanced properties for advanced statuses
                .ForMember(dest => dest.Attachment, opt => opt.MapFrom(src => src.Attachment))
                .ForMember(dest => dest.OtherAttachments, opt => opt.MapFrom(src => src.OtherAttachments.Select(oa => oa.Attachment)))
                .ForMember(dest => dest.RejectionReason, opt => opt.MapFrom(src => GetRejectionReason(src)))
                
                .ForMember(dest => dest.ParentResolutionCode, opt => opt.MapFrom(src => src.ParentResolution != null ? src.ParentResolution.Code : string.Empty))
                // Action permissions will be set in the handler based on user role and status
                .ForMember(dest => dest.CanDownloadAttachments, opt => opt.Ignore());

            CreateMap<ResolutionMemberVote, Features.ResolutionMemberVotes.Dtos.ResolutionMemberVoteDto>()
             .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
             .ForMember(dest => dest.ResolutionId, opt => opt.MapFrom(src => src.ResolutionId))
             .ForMember(dest => dest.ItemsVote, opt => opt.MapFrom(src => src.ResolutionItemVotes))
             .ForMember(dest => dest.VoteComments, opt => opt.MapFrom(src => src.ResolutionMemberVoteComments))
             .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.CreatedBy))
             .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt))
             ;

            CreateMap<ResolutionItemVote, ResolutionItemVoteDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.ResolutionItemId, opt => opt.MapFrom(src => src.ResolutionItemId))
                .ForMember(dest => dest.VoteResult, opt => opt.MapFrom(src => src.VoteResult))
                .ForMember(dest => dest.VoteResultDisplay, opt => opt.MapFrom<MemberItemVotingResultDisplayResolver>())
                .ForMember(dest => dest.Title, opt => opt.MapFrom(src => src.ResolutionItem.Title))
                .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.ResolutionItem.Description))
                .ForMember(dest => dest.DisplayOrder, opt => opt.MapFrom(src => src.ResolutionItem.DisplayOrder))
                .ForMember(dest => dest.ItemComments, opt => opt.MapFrom(src => src.ResolutionItem.ResolutionItemVoteComments.Where(c => c.BoardMemberID == src.ResolutionMemberVote.BoardMemberID)))
                .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.CreatedBy))
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt));

            CreateMap<ResolutionItemVoteComment, ResolutionItemVoteCommentDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.Comment, opt => opt.MapFrom(src => src.Comment))
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt))
                .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.CreatedBy))
                .ForMember(dest => dest.CreatedByName, opt => opt.MapFrom(src => src.CreatedByUser.FullName))
               ;

            CreateMap<ResolutionMemberVoteComment, ResolutionVoteCommentDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.Comment, opt => opt.MapFrom(src => src.Comment))
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt))
                .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.CreatedBy))
                .ForMember(dest => dest.CreatedByName, opt => opt.MapFrom(src => src.CreatedByUser.FullName))
               ;
            // ResolutionType entity to ResolutionTypeDto
            CreateMap<ResolutionType, ResolutionTypeDto>()
                .ForMember(dest => dest.NameAr, opt => opt.MapFrom(src => src.NameAr))
                .ForMember(dest => dest.NameEn, opt => opt.MapFrom(src => src.NameEn))
                .ForMember(dest => dest.LocalizedName, opt => opt.Ignore()); // Computed property

            // ResolutionStatus entity to ResolutionStatusDto
            CreateMap<ResolutionStatus, ResolutionStatusDto>()
                .ForMember(dest => dest.NameAr, opt => opt.MapFrom(src => src.NameAr))
                .ForMember(dest => dest.NameEn, opt => opt.MapFrom(src => src.NameEn))
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.Value, opt => opt.Ignore()) // Will be set from Resolution.Status
                .ForMember(dest => dest.Description, opt => opt.Ignore()) // Will be set from enum description
                .ForMember(dest => dest.LocalizedName, opt => opt.Ignore()); // Computed property

            // Attachment entity to AttachmentDto
            CreateMap<Domain.Entities.Shared.Attachment, AttachmentDto>()
                .ForMember(dest => dest.FilePath, opt => opt.MapFrom<UserFilesResolver>())
                .ForMember(dest => dest.UploadedBy, opt => opt.MapFrom(src => src.CreatedBy));

            CreateMap<ResolutionStatusHistory, ResolutionStatusHistoryDto>()
                 .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.NewStatus))
                 .ForMember(dest => dest.FullName, opt => opt.MapFrom(src => src.CreatedByUser.FullName));


        }

        /// <summary>
        /// Helper method to get rejection reason from status history
        /// </summary>
        /// <param name="resolution">Resolution entity</param>
        /// <returns>Rejection reason if available</returns>
        private static string? GetRejectionReason(Resolution resolution)
        {
            if (resolution.Status != ResolutionStatusEnum.Rejected)
                return null;

            var rejectionHistory = resolution.ResolutionStatusHistories
                .Where(h => h.NewStatus == ResolutionStatusEnum.Rejected)
                .OrderByDescending(h => h.CreatedAt)
                .FirstOrDefault();

            return rejectionHistory?.RejectionReason;
        }


    }
}